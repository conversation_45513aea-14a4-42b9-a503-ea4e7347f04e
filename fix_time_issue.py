#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复时间字段问题的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def fix_time_issue():
    """修复时间字段问题"""
    print("🔧 修复时间字段问题...")
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.connect_db()
    
    backup_table = "dwd_asset_file_details_backup_20250725_152624"
    
    try:
        # 方案1：创建一个不分区的PRIMARY KEY表
        print("📋 创建不分区的PRIMARY KEY表...")
        
        # 先删除现有表
        drop_sql = "DROP TABLE IF EXISTS dwd_asset_file_details"
        processor.cursor.execute(drop_sql)
        processor.conn.commit()
        
        # 创建不分区的PRIMARY KEY表
        create_sql = """
        CREATE TABLE `dwd_asset_file_details` (
          `path` varchar(65533) NOT NULL COMMENT "文件唯一路径",
          `file_size` bigint(20) NULL COMMENT "文件大小 (Bytes)",
          `cre_dt` datetime NULL COMMENT "文件创建时间",
          `domain` varchar(255) NULL COMMENT "所属领域",
          `dataset_name` varchar(255) NULL COMMENT "所属数据集名称",
          `source_table` varchar(50) NULL COMMENT "数据来源表"
        ) ENGINE=OLAP 
        PRIMARY KEY(`path`)
        DISTRIBUTED BY HASH(`path`) BUCKETS 128 
        PROPERTIES (
        "replication_num" = "3",
        "in_memory" = "false",
        "enable_persistent_index" = "false",
        "replicated_storage" = "true",
        "storage_medium" = "HDD",
        "compression" = "LZ4"
        );
        """
        
        processor.cursor.execute(create_sql)
        processor.conn.commit()
        print("✅ 不分区的PRIMARY KEY表创建成功")
        
        # 迁移数据并处理时间问题
        print("📊 迁移数据并修复时间值...")
        
        migrate_sql = f"""
        INSERT INTO dwd_asset_file_details
        WITH cleaned_data AS (
            SELECT
                path, 
                file_size,
                CASE 
                    WHEN cre_dt IS NULL OR cre_dt < '0001-01-01 00:00:00' 
                    THEN '2025-01-01 00:00:00'  -- 默认时间
                    ELSE cre_dt 
                END as cre_dt,
                domain, 
                dataset_name, 
                source_table
            FROM {backup_table}
            WHERE path IS NOT NULL
        ),
        deduplicated_data AS (
            SELECT
                path, file_size, cre_dt, domain, dataset_name, source_table
            FROM (
                SELECT
                    *,
                    ROW_NUMBER() OVER(PARTITION BY path ORDER BY cre_dt DESC) as rn
                FROM cleaned_data
            ) t
            WHERE rn = 1
        )
        SELECT * FROM deduplicated_data
        """
        
        processor.cursor.execute(migrate_sql)
        processor.conn.commit()
        print("✅ 数据迁移完成")
        
        # 验证结果
        count_sql = "SELECT COUNT(*) FROM dwd_asset_file_details"
        processor.cursor.execute(count_sql)
        count = processor.cursor.fetchone()[0]
        print(f"📊 迁移后记录数: {count:,}")
        
        # 测试MERGE功能
        print("🧪 测试MERGE功能...")
        test_merge_sql = """
        INSERT INTO dwd_asset_file_details
        VALUES ('/test/merge_test.txt', 1024, '2025-07-25 10:00:00', 'test', 'test_dataset', 'test_source')
        ON DUPLICATE KEY UPDATE
            file_size = VALUES(file_size),
            cre_dt = VALUES(cre_dt),
            domain = VALUES(domain),
            dataset_name = VALUES(dataset_name),
            source_table = VALUES(source_table)
        """
        
        processor.cursor.execute(test_merge_sql)
        processor.conn.commit()
        print("✅ MERGE功能测试成功")
        
        # 清理测试数据
        cleanup_sql = "DELETE FROM dwd_asset_file_details WHERE path = '/test/merge_test.txt'"
        processor.cursor.execute(cleanup_sql)
        processor.conn.commit()
        print("✅ 测试数据已清理")
        
        print("\n🎉 修复完成！")
        print("📋 现在可以使用 native_merge 策略:")
        print("   python etl_processor.py --env dev --incremental-strategy native_merge")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
    
    finally:
        processor.close_db()

if __name__ == "__main__":
    fix_time_issue()
