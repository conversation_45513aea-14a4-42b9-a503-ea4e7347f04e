#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Superset数据仓库ETL异步优化测试脚本
用于验证异步任务功能和性能
"""

import os
import sys
import time
import datetime
import logging
import pymysql
import argparse
from typing import Dict, List, Tuple

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor, DB_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('async_test')

class AsyncOptimizationTester:
    """异步优化测试器"""
    
    def __init__(self, env='dev'):
        self.env = env
        self.db_config = DB_CONFIG[env]
        self.conn = None
        self.cursor = None
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.conn = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['db'],
                charset='utf8mb4',
                autocommit=False
            )
            self.cursor = self.conn.cursor(pymysql.cursors.DictCursor)
            logger.info(f"✅ 成功连接到{self.env}环境数据库")
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {str(e)}")
            raise
    
    def close_db(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        logger.info("🔌 数据库连接已关闭")
    
    def test_async_task_submission(self) -> bool:
        """测试异步任务提交功能"""
        logger.info("🧪 测试异步任务提交功能")
        
        try:
            # 创建测试处理器
            processor = SupersetDWETLProcessor(env=self.env)
            processor.connect_db()
            
            # 生成测试任务名
            task_name = processor.generate_task_name("test_table")
            
            # 简单的测试SQL
            test_sql = "SELECT COUNT(*) as test_count FROM information_schema.tables"
            
            # 提交异步任务
            success = processor.submit_async_task(
                sql=test_sql,
                task_name=task_name,
                description="异步任务提交测试",
                query_timeout=60
            )
            
            processor.close_db()
            
            if success:
                logger.info("✅ 异步任务提交测试通过")
                return True
            else:
                logger.error("❌ 异步任务提交测试失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 异步任务提交测试异常: {str(e)}")
            return False
    
    def test_task_monitoring(self) -> bool:
        """测试任务监控功能"""
        logger.info("🧪 测试任务监控功能")
        
        try:
            # 查询最近的任务状态
            status_sql = """
            SELECT
                TASK_NAME,
                STATE,
                CREATE_TIME,
                FINISH_TIME,
                PROGRESS
            FROM information_schema.task_runs
            WHERE CREATE_TIME >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY CREATE_TIME DESC
            LIMIT 5
            """

            self.cursor.execute(status_sql)
            results = self.cursor.fetchall()

            logger.info(f"📊 找到 {len(results)} 个最近的任务记录")

            for result in results:
                logger.info(f"   📋 任务: {result.get('TASK_NAME', 'N/A')}")
                logger.info(f"      状态: {result.get('STATE', 'N/A')}")
                logger.info(f"      创建时间: {result.get('CREATE_TIME', 'N/A')}")
                logger.info(f"      完成时间: {result.get('FINISH_TIME', 'N/A')}")
                if result.get('PROGRESS'):
                    logger.info(f"      进度: {result.get('PROGRESS', 'N/A')}")
            
            logger.info("✅ 任务监控功能测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 任务监控功能测试失败: {str(e)}")
            return False
    
    def test_table_verification(self) -> bool:
        """测试表数据验证功能"""
        logger.info("🧪 测试表数据验证功能")
        
        try:
            # 创建测试处理器
            processor = SupersetDWETLProcessor(env=self.env)
            processor.connect_db()
            
            # 测试DWD表验证
            dwd_result = processor.verify_table_data("dwd_asset_file_details", expected_min_rows=0)
            logger.info(f"   DWD表验证结果: {'✅ 通过' if dwd_result else '❌ 失败'}")
            
            # 测试DWS表验证
            daily_result = processor.verify_table_data("dws_asset_storage_trend_daily", expected_min_rows=0)
            logger.info(f"   DWS日度表验证结果: {'✅ 通过' if daily_result else '❌ 失败'}")
            
            weekly_result = processor.verify_table_data("dws_asset_storage_trend_weekly", expected_min_rows=0)
            logger.info(f"   DWS周度表验证结果: {'✅ 通过' if weekly_result else '❌ 失败'}")
            
            domain_result = processor.verify_table_data("dws_asset_domain_distribution", expected_min_rows=0)
            logger.info(f"   DWS领域表验证结果: {'✅ 通过' if domain_result else '❌ 失败'}")
            
            processor.close_db()
            
            all_passed = all([dwd_result, daily_result, weekly_result, domain_result])
            
            if all_passed:
                logger.info("✅ 表数据验证功能测试通过")
            else:
                logger.warning("⚠️ 部分表数据验证失败，可能是表为空或不存在")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 表数据验证功能测试失败: {str(e)}")
            return False
    
    def test_async_tasks_summary(self) -> bool:
        """测试异步任务状态摘要功能"""
        logger.info("🧪 测试异步任务状态摘要功能")
        
        try:
            # 创建测试处理器
            processor = SupersetDWETLProcessor(env=self.env)
            processor.connect_db()
            
            # 获取任务状态摘要
            summary = processor.get_all_async_tasks_status()
            
            logger.info("📊 异步任务状态摘要:")
            logger.info(f"   总任务数: {summary.get('total_tasks', 0)}")
            logger.info(f"   成功任务: {summary.get('success_tasks', 0)}")
            logger.info(f"   失败任务: {summary.get('failed_tasks', 0)}")
            logger.info(f"   运行中任务: {summary.get('running_tasks', 0)}")
            logger.info(f"   等待中任务: {summary.get('pending_tasks', 0)}")
            
            # 测试日志输出
            processor.log_async_tasks_summary()
            
            processor.close_db()
            
            logger.info("✅ 异步任务状态摘要功能测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 异步任务状态摘要功能测试失败: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        logger.info("🚀 开始运行异步优化功能测试")
        logger.info("=" * 60)
        
        # 连接数据库
        self.connect_db()
        
        tests = [
            ("异步任务提交", self.test_async_task_submission),
            ("任务监控功能", self.test_task_monitoring),
            ("表数据验证", self.test_table_verification),
            ("任务状态摘要", self.test_async_tasks_summary),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n🔍 执行测试: {test_name}")
            try:
                result = test_func()
                results[test_name] = result
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"   结果: {status}")
            except Exception as e:
                results[test_name] = False
                logger.error(f"   结果: ❌ 异常 - {str(e)}")
        
        # 关闭数据库连接
        self.close_db()
        
        # 输出测试总结
        logger.info("\n" + "=" * 60)
        logger.info("📋 测试结果总结:")
        
        passed_count = sum(1 for result in results.values() if result)
        total_count = len(results)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"   {test_name}: {status}")
        
        logger.info(f"\n📊 测试统计:")
        logger.info(f"   总测试数: {total_count}")
        logger.info(f"   通过测试: {passed_count}")
        logger.info(f"   失败测试: {total_count - passed_count}")
        logger.info(f"   通过率: {(passed_count/total_count)*100:.1f}%")
        
        if passed_count == total_count:
            logger.info("🎉 所有测试通过！异步优化功能正常")
        else:
            logger.warning("⚠️ 部分测试失败，请检查相关功能")
        
        logger.info("=" * 60)
        
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Superset数据仓库ETL异步优化测试脚本')
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev', help='运行环境 (dev 或 prod)')
    args = parser.parse_args()
    
    logger.info(f"🧪 开始异步优化功能测试")
    logger.info(f"📍 测试环境: {args.env}")
    
    try:
        tester = AsyncOptimizationTester(env=args.env)
        results = tester.run_all_tests()
        
        # 根据测试结果返回退出码
        all_passed = all(results.values())
        return 0 if all_passed else 1
        
    except Exception as e:
        logger.error(f"💥 测试执行失败: {str(e)}")
        return 1

if __name__ == '__main__':
    exit(main())
