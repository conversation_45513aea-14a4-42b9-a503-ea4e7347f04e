#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查表状态脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def check_tables():
    """检查表状态"""
    print("🔍 检查表状态...")
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.connect_db()
    
    try:
        # 查看所有表
        show_tables_sql = "SHOW TABLES"
        processor.cursor.execute(show_tables_sql)
        all_tables = processor.cursor.fetchall()
        
        print("📋 所有表:")
        dwd_tables = []
        for table in all_tables:
            table_name = table[0]
            if 'dwd_asset_file_details' in table_name:
                dwd_tables.append(table_name)
                print(f"   🎯 {table_name}")
            else:
                print(f"   - {table_name}")
        
        print(f"\n📊 找到 {len(dwd_tables)} 个相关表:")
        for table in dwd_tables:
            print(f"   - {table}")
            
            # 检查表结构
            try:
                desc_sql = f"DESC {table}"
                processor.cursor.execute(desc_sql)
                columns = processor.cursor.fetchall()
                print(f"     列数: {len(columns)}")
                
                # 检查记录数
                count_sql = f"SELECT COUNT(*) FROM {table}"
                processor.cursor.execute(count_sql)
                count = processor.cursor.fetchone()[0]
                print(f"     记录数: {count:,}")
                
            except Exception as e:
                print(f"     ❌ 检查失败: {str(e)}")
            
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
    
    finally:
        processor.close_db()

if __name__ == "__main__":
    check_tables()
