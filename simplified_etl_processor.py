#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import datetime
import logging
import pymysql
import argparse
from typing import Dict, List, Tuple, Optional

# 尝试导入配置文件，如果不存在则使用内置配置
try:
    from config_simplified_etl import get_config, get_table_timeout, validate_config
    USE_CONFIG_FILE = True
except ImportError:
    USE_CONFIG_FILE = False

# 创建日志目录 - 适配本地和容器环境
if os.path.exists('/app'):
    LOG_DIR = '/app/logs/superset_dw_etl'
else:
    LOG_DIR = os.path.join(os.getcwd(), 'logs')

LOG_FILE = 'simplified_etl_processor.log'
LOG_PATH = os.path.join(LOG_DIR, LOG_FILE)

# 确保日志目录存在
os.makedirs(LOG_DIR, exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOG_PATH)
    ]
)
logger = logging.getLogger('simplified_superset_dw_etl')

# 数据库配置
DB_CONFIG = {
    'dev': {
        'host': '************',
        'port': 30000,
        'user': 'root',
        'password': '',
        'db': 'CRAWLER'
    },
    'prod': {
        'host': 'kube-starrocks-fe-search.data-infra-prod-ns',
        'port': 9030,
        'user': 'root',
        'password': '',
        'db': 'CRAWLER'
    }
}

class SimplifiedETLProcessor:
    def __init__(self, env='dev'):
        self.env = env
        self.db_config = DB_CONFIG[env]
        self.conn = None
        self.cursor = None
        
        # 可配置的重试参数
        self.max_retries = 3
        self.retry_delay = 10
        
        # 任务执行统计
        self.task_stats = {
            'dwd_success': False,
            'dws_daily_success': False,
            'dws_weekly_success': False,
            'dws_domain_success': False,
            'start_time': None,
            'end_time': None,
            'total_duration': 0
        }
        
        logger.info(f"初始化精简ETL处理器，环境: {env}")
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.conn = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['db'],
                charset='utf8mb4',
                autocommit=False
            )
            self.cursor = self.conn.cursor(pymysql.cursors.DictCursor)
            logger.info(f"成功连接到{self.env}环境数据库")
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
    
    def close_db(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        logger.info("数据库连接已关闭")

    def generate_task_name(self, table_name: str) -> str:
        """生成唯一的任务名称"""
        import random
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S_%f')  # 保留完整微秒
        random_suffix = random.randint(1000, 9999)  # 添加随机后缀
        task_name = f"{table_name}_{timestamp}_{random_suffix}"
        return task_name

    def submit_async_task(self, sql: str, task_name: str, description: str, 
                         query_timeout: int = 3600) -> bool:
        """提交异步任务到StarRocks"""
        try:
            logger.info(f"🚀 提交异步任务: {task_name} ({description})")
            logger.info(f"   查询超时设置: {query_timeout}秒")

            # 构建SUBMIT TASK语句，包含超时设置
            submit_sql = f"SUBMIT /*+set_var(query_timeout={query_timeout})*/ TASK {task_name} AS {sql}"

            start_time = time.time()
            self.cursor.execute(submit_sql)
            self.conn.commit()

            duration = time.time() - start_time
            logger.info(f"✅ 任务 {task_name} 提交成功，耗时: {duration:.2f}秒")
            return True

        except Exception as e:
            logger.error(f"❌ 提交任务 {task_name} 失败: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False

    def wait_for_task_completion(self, task_name: str, description: str, 
                                max_wait_seconds: int = 3600) -> bool:
        """等待异步任务完成"""
        logger.info(f"⏳ 等待任务完成: {task_name} ({description})")
        logger.info(f"   最大等待时间: {max_wait_seconds}秒 ({max_wait_seconds/60:.1f}分钟)")

        check_interval = 15  # 每15秒检查一次
        total_waited = 0
        monitor_start_time = time.time()

        while total_waited < max_wait_seconds:
            try:
                # 查询任务状态
                status_sql = f"""
                SELECT TASK_NAME, STATE, CREATE_TIME, FINISH_TIME, ERROR_MESSAGE
                FROM information_schema.task_runs
                WHERE TASK_NAME = '{task_name}'
                ORDER BY CREATE_TIME DESC
                LIMIT 1
                """

                self.cursor.execute(status_sql)
                result = self.cursor.fetchone()

                if not result:
                    if total_waited < 30:
                        logger.info(f"⏳ 任务 {task_name} 正在初始化...")
                    else:
                        logger.warning(f"⚠️ 未找到任务 {task_name} 的执行记录")
                    time.sleep(check_interval)
                    total_waited += check_interval
                    continue

                current_status = result['STATE']
                create_time = result.get('CREATE_TIME', '')
                finish_time = result.get('FINISH_TIME', '')
                error_message = result.get('ERROR_MESSAGE', '')

                # 检查任务状态
                if current_status == 'SUCCESS':
                    total_duration = time.time() - monitor_start_time
                    logger.info(f"🎉 任务 {task_name} 执行成功！")
                    logger.info(f"   创建时间: {create_time}")
                    logger.info(f"   完成时间: {finish_time}")
                    logger.info(f"   总监控时长: {total_duration:.1f}秒")
                    return True

                elif current_status == 'FAILED':
                    total_duration = time.time() - monitor_start_time
                    logger.error(f"💥 任务 {task_name} 执行失败！")
                    logger.error(f"   错误信息: {error_message}")
                    logger.error(f"   创建时间: {create_time}")
                    logger.error(f"   失败时间: {finish_time}")
                    logger.error(f"   总监控时长: {total_duration:.1f}秒")
                    return False

                elif current_status in ['PENDING', 'RUNNING']:
                    # 任务仍在执行中
                    if total_waited % 60 == 0 and total_waited > 0:
                        logger.info(f"💓 任务 {task_name} 仍在执行中 (已等待 {total_waited//60} 分钟)")
                    time.sleep(check_interval)
                    total_waited += check_interval

                else:
                    logger.warning(f"❓ 任务 {task_name} 状态未知: {current_status}")
                    time.sleep(check_interval)
                    total_waited += check_interval

            except Exception as e:
                logger.error(f"❌ 查询任务 {task_name} 状态失败: {str(e)}")
                time.sleep(check_interval)
                total_waited += check_interval

        # 超时处理
        total_duration = time.time() - monitor_start_time
        logger.error(f"⏰ 任务 {task_name} 等待超时！")
        logger.error(f"   设定超时时间: {max_wait_seconds}秒 ({max_wait_seconds/60:.1f}分钟)")
        logger.error(f"   实际等待时间: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)")
        return False

    def execute_async_task(self, sql: str, table_name: str, description: str,
                          query_timeout: int = 3600, max_wait_seconds: int = 7200) -> bool:
        """执行异步SQL任务的完整流程"""
        logger.info(f"🚀 开始执行异步SQL任务: {description}")
        logger.info(f"   目标表: {table_name}")
        logger.info(f"   查询超时: {query_timeout}秒 ({query_timeout/60:.1f}分钟)")
        logger.info(f"   最大等待时间: {max_wait_seconds}秒 ({max_wait_seconds/60:.1f}分钟)")

        # 生成唯一任务名称
        task_name = self.generate_task_name(table_name)

        # 第一步：提交异步任务
        logger.info(f"📋 第一步：提交异步任务")
        task_submitted = self.submit_async_task(
            sql=sql,
            task_name=task_name,
            description=description,
            query_timeout=query_timeout
        )

        if not task_submitted:
            logger.error(f"💥 异步任务提交失败: {description}")
            return False

        # 第二步：等待任务完成
        logger.info(f"⏳ 第二步：等待任务执行完成")
        task_completed = self.wait_for_task_completion(
            task_name=task_name,
            description=description,
            max_wait_seconds=max_wait_seconds
        )

        if not task_completed:
            logger.error(f"💥 异步任务执行失败或超时: {description}")
            return False

        # 第三步：验证数据完整性
        logger.info(f"🔍 第三步：验证数据完整性")
        if not self.verify_table_data(table_name):
            logger.error(f"❌ 数据验证失败: {description}")
            return False

        logger.info(f"🎉 异步SQL任务完成: {description}")
        return True

    def verify_table_data(self, table_name: str) -> bool:
        """验证表数据是否正确写入"""
        try:
            verify_sql = f"SELECT COUNT(*) as row_count FROM {table_name}"
            self.cursor.execute(verify_sql)
            result = self.cursor.fetchone()

            if result:
                row_count = result['row_count']
                logger.info(f"表 {table_name} 验证结果: 总行数 {row_count:,}")
                
                if row_count > 0:
                    logger.info(f"表 {table_name} 数据验证通过")
                    return True
                else:
                    logger.warning(f"表 {table_name} 数据为空，但验证通过")
                    return True
            else:
                logger.error(f"无法获取表 {table_name} 的统计信息")
                return False

        except Exception as e:
            logger.error(f"验证表 {table_name} 数据失败: {str(e)}")
            return False

    def execute_sync_sql(self, sql: str, table_name: str, description: str) -> bool:
        """执行同步SQL任务"""
        logger.info(f"🔄 开始执行同步SQL任务: {description}")
        logger.info(f"   目标表: {table_name}")

        for attempt in range(self.max_retries):
            try:
                logger.info(f"执行SQL ({attempt + 1}/{self.max_retries}): {description}")
                start_time = time.time()

                self.cursor.execute(sql)
                affected_rows = self.cursor.rowcount
                self.conn.commit()

                duration = time.time() - start_time
                logger.info(f"SQL执行成功，影响行数: {affected_rows}, 耗时: {duration:.2f}秒")
                
                # 验证数据
                if self.verify_table_data(table_name):
                    logger.info(f"✅ 同步SQL任务完成: {description}")
                    return True
                else:
                    logger.error(f"❌ 数据验证失败: {description}")
                    return False

            except Exception as e:
                logger.error(f"SQL执行失败 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                if self.conn:
                    self.conn.rollback()

                if attempt < self.max_retries - 1:
                    wait_time = (attempt + 1) * self.retry_delay
                    logger.info(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"SQL执行最终失败: {description}")
                    return False

        return False

    def process_dwd_asset_file_details(self) -> bool:
        """处理DWD层数据 - dwd_asset_file_details (使用异步任务)"""
        logger.info("=" * 60)
        logger.info("🏗️ 开始处理DWD层数据 - dwd_asset_file_details")
        logger.info("=" * 60)

        # 构建DWD层SQL - 全量历史数据处理
        dwd_sql = """
        INSERT OVERWRITE dwd_asset_file_details
        WITH
        -- 1. 整合所有数据源，并标准化字段 (全量历史数据)
        all_sources_unioned AS (
            -- 来源1: tp_crawler_record (核心采集表) - 全量数据
            SELECT
                path, file_size, cre_dt,
                -- 清洗 domain 字段
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'tp_crawler_record' AS source_table,
                1 AS source_priority -- 给予最高优先级
            FROM tp_crawler_record
            WHERE path IS NOT NULL

            UNION ALL

            -- 来源2: tp_compare_crawler_282 (历史数据)
            SELECT
                path, file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'tp_compare_crawler_282' AS source_table,
                2 AS source_priority
            FROM tp_compare_crawler_282
            WHERE path IS NOT NULL

            UNION ALL

            -- 来源3: mutimodel_processed (历史数据，处理路径前缀)
            SELECT
                SUBSTRING(path, LENGTH('/cpfs01/projects-HDD/cfff-85cad58c20e7_HDD/public') + 2) AS path,
                file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'mutimodel_processed' AS source_table,
                2 AS source_priority
            FROM mutimodel_processed
            WHERE path IS NOT NULL

            UNION ALL

            -- 来源4: crawler_filescan_extra_hwy (历史数据，处理路径前缀)
            SELECT
                SUBSTRING(path, LENGTH('/cfff-4a8d9af84f66_HDD/public') + 2) AS path,
                file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'crawler_filescan_extra_hwy' AS source_table,
                2 AS source_priority
            FROM crawler_filescan_extra_hwy
            WHERE path IS NOT NULL

            UNION ALL

            -- 来源5: fuxi_scan (历史数据，处理复杂路径前缀)
            SELECT
                CASE
                    WHEN path LIKE '/cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD/public%' THEN SUBSTRING(path, LENGTH('/cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD/public') + 2)
                    WHEN path LIKE '/cpfs01/projects-HDD/cfff-01ff502a0784_HDD/public%' THEN SUBSTRING(path, LENGTH('/cpfs01/projects-HDD/cfff-01ff502a0784_HDD/public') + 2)
                    ELSE path
                END AS path,
                file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'fuxi_scan' AS source_table,
                2 AS source_priority
            FROM fuxi_scan
            WHERE path IS NOT NULL

            UNION ALL

            -- 来源6: crawler_filescan_extra (历史数据，处理路径前缀)
            SELECT
                SUBSTRING(path, LENGTH('/cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD/public') + 2) AS path,
                file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'crawler_filescan_extra' AS source_table,
                2 AS source_priority
            FROM crawler_filescan_extra
            WHERE path IS NOT NULL
        ),
        -- 2. 按 path 去重，优先保留 source_priority 高的，然后是最新的记录
        deduplicated_final AS (
            SELECT
                path,
                file_size,
                cre_dt,
                domain,
                dataset_name,
                source_table
            FROM (
                SELECT
                    *,
                    ROW_NUMBER() OVER(PARTITION BY path ORDER BY source_priority ASC, cre_dt DESC) as rn
                FROM all_sources_unioned
            ) t
            WHERE rn = 1
        )
        -- 3. 最终选择所需字段插入 DWD 表
        SELECT
            xx_hash3_128(path) as hash_key,
            path,
            file_size,
            cre_dt,
            domain,
            dataset_name,
            source_table
        FROM deduplicated_final
        """

        # 执行异步DWD层SQL任务，设置较长的超时时间
        success = self.execute_async_task(
            sql=dwd_sql,
            table_name="dwd_asset_file_details",
            description="DWD层数据处理 (全量历史数据)",
            query_timeout=14400,  # 4小时超时
            max_wait_seconds=18000  # 最大等待5小时
        )

        self.task_stats['dwd_success'] = success

        if success:
            logger.info("🎉 DWD层数据处理完成，可以开始执行DWS层")
        else:
            logger.error("💥 DWD层数据处理失败，终止ETL流程")

        return success

    def process_dws_asset_storage_trend_daily(self) -> bool:
        """处理DWS日度趋势表 - dws_asset_storage_trend_daily (使用同步INSERT OVERWRITE)"""
        logger.info("📊 开始处理DWS日度趋势表 - dws_asset_storage_trend_daily")

        daily_sql = """
        INSERT OVERWRITE dws_asset_storage_trend_daily
        WITH
        daily_agg AS (
            -- 按天聚合每日新增量
            SELECT
                DATE(cre_dt) as stat_date,
                SUM(file_size) / POWER(1024, 4) as daily_storage_tb
            FROM dwd_asset_file_details
            GROUP BY DATE(cre_dt)
        ),
        cumulative_agg AS (
            -- 计算每日累计量
            SELECT
                stat_date,
                daily_storage_tb,
                SUM(daily_storage_tb) OVER (ORDER BY stat_date ASC) as cumulative_storage_tb
            FROM daily_agg
        ),
        dod_calcs AS (
            -- 计算日环比
            SELECT
                stat_date,
                daily_storage_tb,
                cumulative_storage_tb,
                LAG(daily_storage_tb, 1, 0) OVER (ORDER BY stat_date) as prev_day_storage,
                LAG(cumulative_storage_tb, 1, 0) OVER (ORDER BY stat_date) as prev_day_cumulative
            FROM cumulative_agg
        )
        SELECT
            stat_date,
            -- 计算日环比百分比，分母为0时处理
            CAST(daily_storage_tb AS DECIMAL(18,6)) AS daily_storage_tb,
            CAST(cumulative_storage_tb AS DECIMAL(18,6)) AS cumulative_storage_tb,
            CAST(IF(prev_day_storage = 0, 0, (daily_storage_tb - prev_day_storage) * 100.0 / prev_day_storage) AS DECIMAL(18,2)) AS daily_dod_pct,
            CAST(IF(prev_day_cumulative = 0, 0, (cumulative_storage_tb - prev_day_cumulative) * 100.0 / prev_day_cumulative) AS DECIMAL(18,2)) AS cumulative_dod_pct
        FROM dod_calcs
        """

        success = self.execute_sync_sql(
            sql=daily_sql,
            table_name="dws_asset_storage_trend_daily",
            description="DWS日度趋势表处理"
        )

        self.task_stats['dws_daily_success'] = success
        return success

    def process_dws_asset_storage_trend_weekly(self) -> bool:
        """处理DWS周度趋势表 - dws_asset_storage_trend_weekly (使用同步INSERT OVERWRITE)"""
        logger.info("📈 开始处理DWS周度趋势表 - dws_asset_storage_trend_weekly")

        weekly_sql = """
        INSERT OVERWRITE dws_asset_storage_trend_weekly
        WITH
        weekly_agg AS (
            -- 按周聚合每周新增量
            SELECT
                DATE_TRUNC('week', cre_dt) as week_start_date,
                SUM(file_size) / POWER(1024, 4) as weekly_storage_tb
            FROM dwd_asset_file_details
            GROUP BY DATE_TRUNC('week', cre_dt)
        ),
        cumulative_agg AS (
            -- 计算每周累计量
            SELECT
                week_start_date,
                weekly_storage_tb,
                SUM(weekly_storage_tb) OVER (ORDER BY week_start_date ASC) as cumulative_storage_tb
            FROM weekly_agg
        ),
        wow_calcs AS (
            -- 计算周环比
            SELECT
                week_start_date,
                weekly_storage_tb,
                cumulative_storage_tb,
                LAG(weekly_storage_tb, 1, 0) OVER (ORDER BY week_start_date) as prev_week_storage,
                LAG(cumulative_storage_tb, 1, 0) OVER (ORDER BY week_start_date) as prev_week_cumulative
            FROM cumulative_agg
        )
        SELECT
            week_start_date,
            weekly_storage_tb,
            cumulative_storage_tb,
            -- 计算周环比百分比
            IF(prev_week_storage = 0, 0, (weekly_storage_tb - prev_week_storage) * 100.0 / prev_week_storage) AS weekly_wow_pct,
            -- 计算累计量周环比百分比
            IF(prev_week_cumulative = 0, 0, (cumulative_storage_tb - prev_week_cumulative) * 100.0 / prev_week_cumulative) AS cumulative_wow_pct
        FROM wow_calcs
        """

        success = self.execute_sync_sql(
            sql=weekly_sql,
            table_name="dws_asset_storage_trend_weekly",
            description="DWS周度趋势表处理"
        )

        self.task_stats['dws_weekly_success'] = success
        return success

    def process_dws_asset_domain_distribution(self) -> bool:
        """处理DWS领域分布表 - dws_asset_domain_distribution (使用同步INSERT OVERWRITE)"""
        logger.info("🌐 开始处理DWS领域分布表 - dws_asset_domain_distribution")

        domain_sql = """
        INSERT OVERWRITE dws_asset_domain_distribution
        SELECT
            domain,
            COUNT(DISTINCT dataset_name) as dataset_count,
            SUM(file_size) / POWER(1024, 4) as size_in_tb
        FROM dwd_asset_file_details
        WHERE domain IS NOT NULL
        GROUP BY domain
        """

        success = self.execute_sync_sql(
            sql=domain_sql,
            table_name="dws_asset_domain_distribution",
            description="DWS领域分布表处理"
        )

        self.task_stats['dws_domain_success'] = success
        return success

    def run(self):
        """执行完整的精简ETL流程"""
        self.task_stats['start_time'] = datetime.datetime.now()

        try:
            logger.info("=" * 80)
            logger.info("🎯 开始执行精简Superset数据仓库ETL任务")
            logger.info(f"📍 执行环境: {self.env}")
            logger.info(f"⏰ 开始时间: {self.task_stats['start_time']}")
            logger.info("=" * 80)

            # 连接数据库
            logger.info("🔌 连接数据库...")
            self.connect_db()
            logger.info("✅ 数据库连接成功")

            # 第一阶段：处理DWD层 (异步任务)
            logger.info("\n" + "🏗️  第一阶段：处理DWD层数据 (异步任务)")
            logger.info("📋 DWD层将整合6个数据源并进行数据清洗去重")

            dwd_start_time = time.time()
            dwd_success = self.process_dwd_asset_file_details()
            dwd_duration = time.time() - dwd_start_time

            if not dwd_success:
                logger.error("💥 DWD层处理失败，终止ETL流程")
                logger.error(f"⏱️  DWD层执行耗时: {dwd_duration:.2f}秒")
                return

            logger.info(f"🎉 DWD层处理成功，耗时: {dwd_duration:.2f}秒")
            logger.info("✅ 数据依赖满足，可以开始处理DWS层")

            # 第二阶段：串行处理DWS层 (同步任务)
            logger.info("\n" + "⚡ 第二阶段：串行处理DWS层数据 (同步任务)")
            logger.info("📊 DWS层将串行处理3个汇总表：日度趋势、周度趋势、领域分布")

            dws_start_time = time.time()

            # 串行执行DWS层任务
            dws_tasks = [
                ('日度趋势表', self.process_dws_asset_storage_trend_daily),
                ('周度趋势表', self.process_dws_asset_storage_trend_weekly),
                ('领域分布表', self.process_dws_asset_domain_distribution)
            ]

            dws_results = {}
            for i, (task_name, task_func) in enumerate(dws_tasks, 1):
                logger.info(f"\n🔄 执行第 {i}/{len(dws_tasks)} 个DWS任务: {task_name}")

                task_individual_start = time.time()
                try:
                    result = task_func()
                    dws_results[task_name] = result
                    task_individual_duration = time.time() - task_individual_start

                    if result:
                        logger.info(f"✅ DWS任务完成 [{i}/{len(dws_tasks)}]: {task_name}")
                        logger.info(f"   耗时: {task_individual_duration:.1f}秒")
                    else:
                        logger.error(f"❌ DWS任务失败 [{i}/{len(dws_tasks)}]: {task_name}")
                        logger.error(f"   耗时: {task_individual_duration:.1f}秒")

                except Exception as e:
                    dws_results[task_name] = False
                    task_individual_duration = time.time() - task_individual_start
                    logger.error(f"💥 DWS任务异常 [{i}/{len(dws_tasks)}]: {task_name}")
                    logger.error(f"   异常信息: {str(e)}")
                    logger.error(f"   耗时: {task_individual_duration:.1f}秒")

            dws_duration = time.time() - dws_start_time

            # 检查DWS层整体结果
            dws_success_count = sum(1 for success in dws_results.values() if success)
            dws_total_count = len(dws_results)

            logger.info(f"📈 DWS层串行处理耗时: {dws_duration:.1f}秒 ({dws_duration/60:.1f}分钟)")

            if dws_success_count == dws_total_count:
                logger.info("🎊 所有DWS层任务执行成功！")
            else:
                logger.warning(f"⚠️  DWS层部分任务失败: {dws_success_count}/{dws_total_count}")

            # 任务完成
            self.task_stats['end_time'] = datetime.datetime.now()
            self.task_stats['total_duration'] = (
                self.task_stats['end_time'] - self.task_stats['start_time']
            ).total_seconds()

            # 打印执行总结
            self.print_summary()

        except Exception as e:
            logger.error(f"💥 ETL流程执行失败: {str(e)}")
            self.task_stats['end_time'] = datetime.datetime.now()
            self.task_stats['total_duration'] = (
                self.task_stats['end_time'] - self.task_stats['start_time']
            ).total_seconds() if self.task_stats['start_time'] else 0
            raise
        finally:
            # 关闭数据库连接
            logger.info("🔌 关闭数据库连接...")
            self.close_db()

    def print_summary(self):
        """打印任务执行总结"""
        logger.info("=" * 80)
        logger.info("📋 精简ETL任务执行总结报告")
        logger.info("=" * 80)

        # 基本信息
        logger.info("⏰ 执行时间信息:")
        logger.info(f"   开始时间: {self.task_stats['start_time']}")
        logger.info(f"   结束时间: {self.task_stats['end_time']}")
        logger.info(f"   总耗时: {self.task_stats['total_duration']:.2f} 秒")

        # 任务执行结果
        logger.info("\n📊 任务执行结果:")

        # DWD层结果
        dwd_icon = "✅" if self.task_stats['dwd_success'] else "❌"
        logger.info(f"   {dwd_icon} DWD层处理 (dwd_asset_file_details): {'成功' if self.task_stats['dwd_success'] else '失败'}")

        # DWS层结果
        dws_tasks = [
            ('DWS日度趋势 (dws_asset_storage_trend_daily)', self.task_stats['dws_daily_success']),
            ('DWS周度趋势 (dws_asset_storage_trend_weekly)', self.task_stats['dws_weekly_success']),
            ('DWS领域分布 (dws_asset_domain_distribution)', self.task_stats['dws_domain_success'])
        ]

        for task_name, success in dws_tasks:
            icon = "✅" if success else "❌"
            status = "成功" if success else "失败"
            logger.info(f"   {icon} {task_name}: {status}")

        # 统计信息
        total_tasks = 4
        success_tasks = sum([
            self.task_stats['dwd_success'],
            self.task_stats['dws_daily_success'],
            self.task_stats['dws_weekly_success'],
            self.task_stats['dws_domain_success']
        ])

        success_rate = (success_tasks / total_tasks) * 100

        logger.info("\n📈 执行统计:")
        logger.info(f"   总任务数: {total_tasks}")
        logger.info(f"   成功任务: {success_tasks}")
        logger.info(f"   失败任务: {total_tasks - success_tasks}")
        logger.info(f"   成功率: {success_rate:.1f}%")

        # 最终状态
        logger.info("\n🎯 最终状态:")
        if success_rate == 100:
            logger.info("   🎉 所有ETL任务执行成功！数据仓库更新完成！")
        elif success_rate >= 75:
            logger.warning("   ⚠️  大部分ETL任务执行成功，请检查失败的任务")
        else:
            logger.error("   ❌ 多个ETL任务执行失败，数据仓库更新不完整")

        logger.info("=" * 80)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='精简Superset数据仓库ETL处理脚本')
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev', help='运行环境 (dev 或 prod)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='日志级别')
    parser.add_argument('--max-retries', type=int, default=3, help='最大重试次数')
    parser.add_argument('--retry-delay', type=int, default=10, help='重试延迟时间(秒)')
    args = parser.parse_args()

    # 设置日志级别
    logging.getLogger('simplified_superset_dw_etl').setLevel(getattr(logging, args.log_level))

    logger.info(f"🚀 开始执行精简Superset数据仓库ETL任务")
    logger.info(f"📍 运行环境: {args.env}")
    logger.info(f"📊 日志级别: {args.log_level}")
    logger.info(f"🔄 最大重试次数: {args.max_retries}")
    logger.info(f"⏱️  重试延迟: {args.retry_delay}秒")

    logger.info("💡 精简版特点:")
    logger.info("   - DWD层使用异步任务处理全量历史数据")
    logger.info("   - DWS层使用同步INSERT OVERWRITE，确保数据一致性")
    logger.info("   - 简化的错误处理和重试机制")
    logger.info("   - 专注处理4张核心表")

    try:
        processor = SimplifiedETLProcessor(env=args.env)
        processor.max_retries = args.max_retries
        processor.retry_delay = args.retry_delay

        processor.run()
        logger.info("🎉 精简ETL任务执行完成")
        return 0
    except Exception as e:
        logger.error(f"💥 精简ETL任务执行失败: {str(e)}")
        return 1


if __name__ == '__main__':
    exit(main())


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='精简Superset数据仓库ETL处理脚本')
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev', help='运行环境 (dev 或 prod)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='日志级别')
    parser.add_argument('--max-retries', type=int, default=3, help='最大重试次数')
    parser.add_argument('--retry-delay', type=int, default=10, help='重试延迟时间(秒)')
    args = parser.parse_args()

    # 设置日志级别
    logging.getLogger('simplified_superset_dw_etl').setLevel(getattr(logging, args.log_level))

    logger.info(f"🚀 开始执行精简Superset数据仓库ETL任务")
    logger.info(f"📍 运行环境: {args.env}")
    logger.info(f"📊 日志级别: {args.log_level}")
    logger.info(f"🔄 最大重试次数: {args.max_retries}")
    logger.info(f"⏱️  重试延迟: {args.retry_delay}秒")

    logger.info("💡 精简版特点:")
    logger.info("   - DWD层使用异步任务处理全量历史数据")
    logger.info("   - DWS层使用同步INSERT OVERWRITE，确保数据一致性")
    logger.info("   - 简化的错误处理和重试机制")
    logger.info("   - 专注处理4张核心表")

    try:
        processor = SimplifiedETLProcessor(env=args.env)
        processor.max_retries = args.max_retries
        processor.retry_delay = args.retry_delay

        processor.run()
        logger.info("🎉 精简ETL任务执行完成")
        return 0
    except Exception as e:
        logger.error(f"💥 精简ETL任务执行失败: {str(e)}")
        return 1


if __name__ == '__main__':
    exit(main())
