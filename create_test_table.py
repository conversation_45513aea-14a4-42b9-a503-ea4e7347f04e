#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建测试表脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def create_test_table():
    """创建测试表"""
    print("🔧 创建测试表...")
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.connect_db()
    
    try:
        # 删除已存在的表
        drop_sql = "DROP TABLE IF EXISTS dwd_asset_file_details"
        processor.cursor.execute(drop_sql)
        processor.conn.commit()
        print("✅ 已删除现有表")
        
        # 创建测试表（DUPLICATE KEY）
        create_sql = """
        CREATE TABLE `dwd_asset_file_details` (
          `path` varchar(65533) NOT NULL COMMENT "文件唯一路径",
          `file_size` bigint(20) NULL COMMENT "文件大小 (Bytes)",
          `cre_dt` datetime NULL COMMENT "文件创建时间",
          `domain` varchar(255) NULL COMMENT "所属领域",
          `dataset_name` varchar(255) NULL COMMENT "所属数据集名称",
          `source_table` varchar(50) NULL COMMENT "数据来源表"
        ) ENGINE=OLAP 
        DUPLICATE KEY(`path`)
        PARTITION BY time_slice(cre_dt, 1, 'day', 'floor')
        DISTRIBUTED BY HASH(`path`) BUCKETS 128 
        PROPERTIES (
        "replication_num" = "3",
        "in_memory" = "false",
        "enable_persistent_index" = "false",
        "replicated_storage" = "true",
        "storage_medium" = "HDD",
        "compression" = "LZ4"
        );
        """
        
        processor.cursor.execute(create_sql)
        processor.conn.commit()
        print("✅ 测试表创建成功（DUPLICATE KEY）")
        
        # 插入一些测试数据
        insert_sql = """
        INSERT INTO dwd_asset_file_details VALUES
        ('/test/file1.txt', 1024, '2025-07-25 10:00:00', 'test', 'test_dataset', 'test_source'),
        ('/test/file2.txt', 2048, '2025-07-25 11:00:00', 'test', 'test_dataset', 'test_source'),
        ('/test/file1.txt', 1536, '2025-07-24 10:00:00', 'test', 'test_dataset', 'test_source')
        """
        
        processor.cursor.execute(insert_sql)
        processor.conn.commit()
        print("✅ 测试数据插入成功")
        
        # 检查数据
        check_sql = "SELECT COUNT(*) as total, COUNT(DISTINCT path) as unique_paths FROM dwd_asset_file_details"
        processor.cursor.execute(check_sql)
        result = processor.cursor.fetchone()
        print(f"📊 测试数据统计: 总记录数={result[0]}, 唯一path数={result[1]}")
        
    except Exception as e:
        print(f"❌ 创建测试表失败: {str(e)}")
    
    finally:
        processor.close_db()

if __name__ == "__main__":
    create_test_table()
