#!/bin/bash

# Superset数据仓库ETL任务运行脚本
# 使用方法: ./run_etl.sh [dev|prod] [重试次数] [重试延迟]

set -e

# 默认参数
ENV=${1:-dev}
MAX_RETRIES=${2:-8}
RETRY_DELAY=${3:-60}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境参数
if [[ "$ENV" != "dev" && "$ENV" != "prod" ]]; then
    log_error "无效的环境参数: $ENV"
    log_info "使用方法: ./run_etl.sh [dev|prod] [重试次数] [重试延迟]"
    exit 1
fi

log_info "开始执行Superset数据仓库ETL任务"
log_info "运行环境: $ENV"
log_info "最大重试次数: $MAX_RETRIES"
log_info "重试延迟: ${RETRY_DELAY}秒"

# 检查Python环境
if ! command -v python &> /dev/null; then
    log_error "Python 未安装或不在PATH中"
    exit 1
fi

# 检查依赖
if ! python -c "import pymysql" &> /dev/null; then
    log_warning "pymysql 未安装，正在安装..."
    pip install pymysql
fi

# 执行ETL任务
log_info "开始执行ETL任务..."
start_time=$(date +%s)

if python etl_processor.py \
    --env "$ENV" \
    --log-level INFO \
    --max-retries "$MAX_RETRIES" \
    --retry-delay "$RETRY_DELAY"; then
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    log_success "ETL任务执行成功！"
    log_info "总耗时: ${duration}秒"
    
    # 显示日志文件位置
    log_info "日志文件位置:"
    if [[ -d "logs" ]]; then
        latest_log=$(ls -t logs/etl_processor-*.log 2>/dev/null | head -1)
        if [[ -n "$latest_log" ]]; then
            echo "  📄 $latest_log"
        fi
    fi
    
    exit 0
else
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    log_error "ETL任务执行失败！"
    log_info "总耗时: ${duration}秒"
    
    # 显示错误日志
    log_info "错误排查建议:"
    echo "  1. 检查StarRocks集群状态和负载"
    echo "  2. 确认数据库连接配置正确"
    echo "  3. 在非高峰期重新执行任务"
    echo "  4. 增加重试次数和延迟时间"
    echo "  5. 查看详细日志文件排查具体错误"
    
    exit 1
fi
