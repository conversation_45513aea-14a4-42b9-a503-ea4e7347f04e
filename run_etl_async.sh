#!/bin/bash

# Superset数据仓库ETL异步任务执行脚本
# 使用优化后的异步任务模式，提供更好的性能和稳定性

set -e  # 遇到错误立即退出

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ETL_SCRIPT="$SCRIPT_DIR/etl_processor.py"
LOG_DIR="$SCRIPT_DIR/logs"

# 默认参数
ENV="dev"
LOG_LEVEL="INFO"
MAX_RETRIES=5
RETRY_DELAY=30
USE_ASYNC=true
USE_PARALLEL=false
INIT_MODE=false
TARGET_DATE=""

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Superset数据仓库ETL异步任务执行脚本

用法: $0 [选项]

选项:
    -e, --env ENV           运行环境 (dev|prod) [默认: dev]
    -l, --log-level LEVEL   日志级别 (DEBUG|INFO|WARNING|ERROR) [默认: INFO]
    -r, --max-retries NUM   最大重试次数 [默认: 5]
    -d, --retry-delay SEC   重试延迟时间(秒) [默认: 30]
    -s, --sync-mode         使用同步模式 (默认使用异步模式)
    -p, --parallel          DWS层使用并行模式 (默认串行)
    -i, --init-mode         初始化模式：处理全量历史数据 (默认增量模式)
    -t, --target-date DATE  目标处理日期 (格式: YYYY-MM-DD，默认当日)
    -h, --help              显示此帮助信息

示例:
    # 使用默认配置 (开发环境，异步模式，串行DWS)
    $0

    # 生产环境，异步模式，并行DWS
    $0 --env prod --parallel

    # 开发环境，同步模式，调试日志
    $0 --env dev --sync-mode --log-level DEBUG

    # 生产环境，异步模式，增加重试次数
    $0 --env prod --max-retries 10 --retry-delay 60

    # 初始化模式：处理全量历史数据
    $0 --env prod --init-mode

    # 增量模式：处理指定日期数据
    $0 --env prod --target-date 2024-12-17

特性说明:
    ✅ 异步任务模式 (默认)
       - 使用INSERT OVERWRITE替代TRUNCATE+INSERT
       - 支持长时间运行任务，避免会话超时
       - 提供详细的任务监控和状态跟踪
       - 更好的错误处理和重试机制

    🔄 同步模式 (兼容性)
       - 传统的TRUNCATE+INSERT模式
       - 适用于小数据量或测试场景

    🏗️ 初始化模式
       - 处理全量历史数据，包含所有6个数据源
       - 执行时间较长，适用于首次建表或数据重建
       - 建议在非高峰期运行

    🔄 增量模式 (默认)
       - 只处理当日新增数据，主要来源tp_crawler_record表
       - 执行时间短，适合日常定时任务
       - 支持重跑，相同路径优先保留当日数据

    ⚡ DWS层执行模式
       - 串行模式 (推荐): 避免资源竞争，执行稳定
       - 并行模式: 可能更快，但在异步环境下可能不稳定

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV="$2"
            shift 2
            ;;
        -l|--log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        -r|--max-retries)
            MAX_RETRIES="$2"
            shift 2
            ;;
        -d|--retry-delay)
            RETRY_DELAY="$2"
            shift 2
            ;;
        -s|--sync-mode)
            USE_ASYNC=false
            shift
            ;;
        -p|--parallel)
            USE_PARALLEL=true
            shift
            ;;
        -i|--init-mode)
            INIT_MODE=true
            shift
            ;;
        -t|--target-date)
            TARGET_DATE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ "$ENV" != "dev" && "$ENV" != "prod" ]]; then
    print_error "无效的环境参数: $ENV (必须是 dev 或 prod)"
    exit 1
fi

# 验证日志级别
if [[ "$LOG_LEVEL" != "DEBUG" && "$LOG_LEVEL" != "INFO" && "$LOG_LEVEL" != "WARNING" && "$LOG_LEVEL" != "ERROR" ]]; then
    print_error "无效的日志级别: $LOG_LEVEL"
    exit 1
fi

# 验证目标日期格式
if [[ -n "$TARGET_DATE" ]]; then
    if ! date -d "$TARGET_DATE" >/dev/null 2>&1; then
        print_error "无效的日期格式: $TARGET_DATE (应为 YYYY-MM-DD 格式)"
        exit 1
    fi
fi

# 初始化模式和目标日期不能同时指定
if [[ "$INIT_MODE" == "true" && -n "$TARGET_DATE" ]]; then
    print_error "初始化模式和目标日期不能同时指定"
    exit 1
fi

# 检查Python脚本是否存在
if [[ ! -f "$ETL_SCRIPT" ]]; then
    print_error "ETL脚本不存在: $ETL_SCRIPT"
    exit 1
fi

# 创建日志目录
mkdir -p "$LOG_DIR"

# 构建Python命令
PYTHON_CMD="python3 $ETL_SCRIPT"
PYTHON_CMD="$PYTHON_CMD --env $ENV"
PYTHON_CMD="$PYTHON_CMD --log-level $LOG_LEVEL"
PYTHON_CMD="$PYTHON_CMD --max-retries $MAX_RETRIES"
PYTHON_CMD="$PYTHON_CMD --retry-delay $RETRY_DELAY"

if [[ "$USE_ASYNC" == "false" ]]; then
    PYTHON_CMD="$PYTHON_CMD --sync-mode"
fi

if [[ "$USE_PARALLEL" == "true" ]]; then
    PYTHON_CMD="$PYTHON_CMD --dws-parallel"
fi

if [[ "$INIT_MODE" == "true" ]]; then
    PYTHON_CMD="$PYTHON_CMD --init-mode"
fi

if [[ -n "$TARGET_DATE" ]]; then
    PYTHON_CMD="$PYTHON_CMD --target-date $TARGET_DATE"
fi

# 显示执行信息
print_info "=== Superset数据仓库ETL任务执行 ==="
print_info "环境: $ENV"
print_info "日志级别: $LOG_LEVEL"
print_info "最大重试次数: $MAX_RETRIES"
print_info "重试延迟: ${RETRY_DELAY}秒"
print_info "执行模式: $([ "$USE_ASYNC" == "true" ] && echo "异步任务模式" || echo "同步模式")"
print_info "DWS层模式: $([ "$USE_PARALLEL" == "true" ] && echo "并行模式" || echo "串行模式")"
print_info "数据处理模式: $([ "$INIT_MODE" == "true" ] && echo "初始化模式 (全量历史数据)" || echo "增量模式 (当日数据)")"
if [[ -n "$TARGET_DATE" ]]; then
    print_info "目标处理日期: $TARGET_DATE"
else
    print_info "目标处理日期: $(date '+%Y-%m-%d') (当日)"
fi
print_info "执行命令: $PYTHON_CMD"
print_info "=================================="

# 记录开始时间
START_TIME=$(date +%s)
START_TIME_STR=$(date '+%Y-%m-%d %H:%M:%S')

print_info "开始时间: $START_TIME_STR"

# 执行ETL任务
print_info "正在执行ETL任务..."
if eval "$PYTHON_CMD"; then
    # 计算执行时间
    END_TIME=$(date +%s)
    END_TIME_STR=$(date '+%Y-%m-%d %H:%M:%S')
    DURATION=$((END_TIME - START_TIME))
    DURATION_MIN=$((DURATION / 60))
    DURATION_SEC=$((DURATION % 60))
    
    print_success "ETL任务执行成功！"
    print_info "结束时间: $END_TIME_STR"
    print_info "总耗时: ${DURATION_MIN}分${DURATION_SEC}秒"
    
    # 显示日志文件位置
    if [[ -d "$LOG_DIR" ]]; then
        LATEST_LOG=$(find "$LOG_DIR" -name "*.log" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
        if [[ -n "$LATEST_LOG" ]]; then
            print_info "详细日志: $LATEST_LOG"
        fi
    fi
    
    exit 0
else
    # 计算执行时间
    END_TIME=$(date +%s)
    END_TIME_STR=$(date '+%Y-%m-%d %H:%M:%S')
    DURATION=$((END_TIME - START_TIME))
    DURATION_MIN=$((DURATION / 60))
    DURATION_SEC=$((DURATION % 60))
    
    print_error "ETL任务执行失败！"
    print_info "结束时间: $END_TIME_STR"
    print_info "执行时长: ${DURATION_MIN}分${DURATION_SEC}秒"
    
    # 显示日志文件位置
    if [[ -d "$LOG_DIR" ]]; then
        LATEST_LOG=$(find "$LOG_DIR" -name "*.log" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
        if [[ -n "$LATEST_LOG" ]]; then
            print_error "错误日志: $LATEST_LOG"
            print_info "查看最后几行日志:"
            tail -20 "$LATEST_LOG" | while read line; do
                echo "  $line"
            done
        fi
    fi
    
    exit 1
fi
