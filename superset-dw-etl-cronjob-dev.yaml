apiVersion: batch/v1
kind: CronJob
metadata:
  name: superset-dw-etl-job-dev
  namespace: data-assets-dev-ns  # 根据实际情况修改
spec:
  schedule: "*/30 * * * *"  # 开发环境每30分钟执行一次，便于测试
  concurrencyPolicy: Forbid  # 不允许并发执行
  successfulJobsHistoryLimit: 5
  failedJobsHistoryLimit: 5
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: superset-dw-etl-dev
            image: harbor.internal.sais.com.cn/assets-platform/python:3.11-slim
            workingDir: /app
            command:
            - /bin/sh
            - -c
            - |
              pip install -r requirements.txt && \
              python etl_processor.py --env dev --debug --log-level DEBUG
            volumeMounts:
              - mountPath: /app
                name: app-volume
            env:
            - name: TZ
              value: "Asia/Shanghai"
            resources:
              requests:
                memory: "256Mi"
                cpu: "250m"
              limits:
                memory: "1Gi"
                cpu: "1000m"
          volumes:
            - name: app-volume
              configMap:
                name: superset-dw-etl-files-dev
                defaultMode: 0755
          restartPolicy: OnFailure
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: superset-dw-etl-files-dev
  namespace: data-assets-dev-ns  # 根据实际情况修改
data:
  etl_processor.py: |
    # 这里需要将etl_processor.py的内容复制过来
    # 或者使用kubectl create configmap命令创建
  requirements.txt: |
    pymysql==1.1.0
