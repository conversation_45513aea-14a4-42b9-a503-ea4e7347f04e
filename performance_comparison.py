#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ETL性能对比脚本
对比同步模式和异步模式的性能差异
"""

import os
import sys
import time
import datetime
import logging
import argparse
import json
from typing import Dict, List, Tuple

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('performance_comparison')

class PerformanceComparator:
    """性能对比器"""
    
    def __init__(self, env='dev'):
        self.env = env
        self.results = {
            'sync_mode': {},
            'async_mode': {},
            'comparison': {}
        }
    
    def run_sync_mode_test(self) -> Dict[str, any]:
        """运行同步模式测试"""
        logger.info("🔄 开始同步模式性能测试")
        
        start_time = time.time()
        
        try:
            processor = SupersetDWETLProcessor(env=self.env)
            processor.use_async_mode = False  # 强制使用同步模式
            processor.connect_db()
            
            # 记录各阶段时间
            dwd_start = time.time()
            dwd_success = processor.process_dwd_layer()
            dwd_duration = time.time() - dwd_start
            
            if dwd_success:
                dws_start = time.time()
                dws_results = processor.process_dws_layer_sequential()
                dws_duration = time.time() - dws_start
                dws_success = all(dws_results.values())
            else:
                dws_duration = 0
                dws_success = False
            
            processor.close_db()
            
            total_duration = time.time() - start_time
            
            result = {
                'success': dwd_success and dws_success,
                'total_duration': total_duration,
                'dwd_duration': dwd_duration,
                'dws_duration': dws_duration,
                'dwd_success': dwd_success,
                'dws_success': dws_success,
                'mode': 'sync'
            }
            
            logger.info(f"✅ 同步模式测试完成，总耗时: {total_duration:.1f}秒")
            return result
            
        except Exception as e:
            total_duration = time.time() - start_time
            logger.error(f"❌ 同步模式测试失败: {str(e)}")
            return {
                'success': False,
                'total_duration': total_duration,
                'error': str(e),
                'mode': 'sync'
            }
    
    def run_async_mode_test(self) -> Dict[str, any]:
        """运行异步模式测试"""
        logger.info("⚡ 开始异步模式性能测试")
        
        start_time = time.time()
        
        try:
            processor = SupersetDWETLProcessor(env=self.env)
            processor.use_async_mode = True  # 使用异步模式
            processor.connect_db()
            
            # 记录各阶段时间
            dwd_start = time.time()
            dwd_success = processor.process_dwd_layer()
            dwd_duration = time.time() - dwd_start
            
            if dwd_success:
                dws_start = time.time()
                dws_results = processor.process_dws_layer_sequential()
                dws_duration = time.time() - dws_start
                dws_success = all(dws_results.values())
            else:
                dws_duration = 0
                dws_success = False
            
            processor.close_db()
            
            total_duration = time.time() - start_time
            
            result = {
                'success': dwd_success and dws_success,
                'total_duration': total_duration,
                'dwd_duration': dwd_duration,
                'dws_duration': dws_duration,
                'dwd_success': dwd_success,
                'dws_success': dws_success,
                'mode': 'async'
            }
            
            logger.info(f"✅ 异步模式测试完成，总耗时: {total_duration:.1f}秒")
            return result
            
        except Exception as e:
            total_duration = time.time() - start_time
            logger.error(f"❌ 异步模式测试失败: {str(e)}")
            return {
                'success': False,
                'total_duration': total_duration,
                'error': str(e),
                'mode': 'async'
            }
    
    def calculate_comparison(self, sync_result: Dict, async_result: Dict) -> Dict[str, any]:
        """计算性能对比"""
        logger.info("📊 计算性能对比数据")
        
        comparison = {
            'timestamp': datetime.datetime.now().isoformat(),
            'environment': self.env,
            'both_successful': sync_result.get('success', False) and async_result.get('success', False)
        }
        
        if comparison['both_successful']:
            sync_total = sync_result['total_duration']
            async_total = async_result['total_duration']
            
            comparison.update({
                'total_time_improvement': {
                    'sync_seconds': sync_total,
                    'async_seconds': async_total,
                    'difference_seconds': sync_total - async_total,
                    'improvement_percentage': ((sync_total - async_total) / sync_total) * 100 if sync_total > 0 else 0
                },
                'dwd_time_comparison': {
                    'sync_seconds': sync_result.get('dwd_duration', 0),
                    'async_seconds': async_result.get('dwd_duration', 0),
                    'difference_seconds': sync_result.get('dwd_duration', 0) - async_result.get('dwd_duration', 0)
                },
                'dws_time_comparison': {
                    'sync_seconds': sync_result.get('dws_duration', 0),
                    'async_seconds': async_result.get('dws_duration', 0),
                    'difference_seconds': sync_result.get('dws_duration', 0) - async_result.get('dws_duration', 0)
                }
            })
        else:
            comparison['error'] = "无法进行对比，因为至少有一种模式执行失败"
        
        return comparison
    
    def print_comparison_report(self, sync_result: Dict, async_result: Dict, comparison: Dict):
        """打印对比报告"""
        logger.info("=" * 80)
        logger.info("📋 ETL性能对比报告")
        logger.info("=" * 80)
        
        # 基本信息
        logger.info(f"🕐 测试时间: {comparison.get('timestamp', 'N/A')}")
        logger.info(f"🌍 测试环境: {comparison.get('environment', 'N/A')}")
        
        # 执行结果
        logger.info("\n📊 执行结果:")
        sync_status = "✅ 成功" if sync_result.get('success', False) else "❌ 失败"
        async_status = "✅ 成功" if async_result.get('success', False) else "❌ 失败"
        
        logger.info(f"   同步模式: {sync_status}")
        logger.info(f"   异步模式: {async_status}")
        
        if sync_result.get('error'):
            logger.info(f"   同步模式错误: {sync_result['error']}")
        if async_result.get('error'):
            logger.info(f"   异步模式错误: {async_result['error']}")
        
        # 性能对比
        if comparison.get('both_successful', False):
            logger.info("\n⏱️ 性能对比:")
            
            total_comp = comparison['total_time_improvement']
            logger.info(f"   总执行时间:")
            logger.info(f"     同步模式: {total_comp['sync_seconds']:.1f}秒")
            logger.info(f"     异步模式: {total_comp['async_seconds']:.1f}秒")
            logger.info(f"     时间差异: {total_comp['difference_seconds']:.1f}秒")
            
            improvement = total_comp['improvement_percentage']
            if improvement > 0:
                logger.info(f"     性能提升: {improvement:.1f}% (异步模式更快)")
            elif improvement < 0:
                logger.info(f"     性能下降: {abs(improvement):.1f}% (同步模式更快)")
            else:
                logger.info(f"     性能相当")
            
            # DWD层对比
            dwd_comp = comparison['dwd_time_comparison']
            logger.info(f"\n   DWD层执行时间:")
            logger.info(f"     同步模式: {dwd_comp['sync_seconds']:.1f}秒")
            logger.info(f"     异步模式: {dwd_comp['async_seconds']:.1f}秒")
            logger.info(f"     时间差异: {dwd_comp['difference_seconds']:.1f}秒")
            
            # DWS层对比
            dws_comp = comparison['dws_time_comparison']
            logger.info(f"\n   DWS层执行时间:")
            logger.info(f"     同步模式: {dws_comp['sync_seconds']:.1f}秒")
            logger.info(f"     异步模式: {dws_comp['async_seconds']:.1f}秒")
            logger.info(f"     时间差异: {dws_comp['difference_seconds']:.1f}秒")
            
        else:
            logger.warning("\n⚠️ 无法进行性能对比，因为至少有一种模式执行失败")
        
        # 建议
        logger.info("\n💡 建议:")
        if comparison.get('both_successful', False):
            improvement = comparison['total_time_improvement']['improvement_percentage']
            if improvement > 10:
                logger.info("   🚀 异步模式显著提升性能，强烈推荐使用")
            elif improvement > 0:
                logger.info("   ✅ 异步模式略有性能提升，推荐使用")
            elif improvement > -10:
                logger.info("   🤔 两种模式性能相近，可根据其他因素选择")
            else:
                logger.info("   ⚠️ 同步模式在此测试中表现更好，需要进一步分析")
            
            logger.info("   📈 异步模式的其他优势:")
            logger.info("     - 更好的会话独立性和稳定性")
            logger.info("     - 详细的任务监控和状态跟踪")
            logger.info("     - 更强的错误处理和恢复能力")
            logger.info("     - 支持长时间运行的任务")
        else:
            logger.info("   🔧 请先解决执行失败的问题，然后重新进行性能测试")
        
        logger.info("=" * 80)
    
    def save_results(self, sync_result: Dict, async_result: Dict, comparison: Dict):
        """保存测试结果到文件"""
        results = {
            'sync_mode': sync_result,
            'async_mode': async_result,
            'comparison': comparison
        }
        
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"performance_comparison_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"📁 测试结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"❌ 保存测试结果失败: {str(e)}")
    
    def run_comparison(self, save_results=True) -> Dict[str, any]:
        """运行完整的性能对比测试"""
        logger.info("🚀 开始ETL性能对比测试")
        logger.info(f"📍 测试环境: {self.env}")
        
        # 运行同步模式测试
        sync_result = self.run_sync_mode_test()
        
        # 等待一段时间，避免数据库负载影响
        logger.info("⏳ 等待5秒后开始异步模式测试...")
        time.sleep(5)
        
        # 运行异步模式测试
        async_result = self.run_async_mode_test()
        
        # 计算对比结果
        comparison = self.calculate_comparison(sync_result, async_result)
        
        # 打印报告
        self.print_comparison_report(sync_result, async_result, comparison)
        
        # 保存结果
        if save_results:
            self.save_results(sync_result, async_result, comparison)
        
        return {
            'sync_mode': sync_result,
            'async_mode': async_result,
            'comparison': comparison
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ETL性能对比测试脚本')
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev', help='运行环境 (dev 或 prod)')
    parser.add_argument('--no-save', action='store_true', help='不保存测试结果到文件')
    args = parser.parse_args()
    
    try:
        comparator = PerformanceComparator(env=args.env)
        results = comparator.run_comparison(save_results=not args.no_save)
        
        # 根据测试结果返回退出码
        sync_success = results['sync_mode'].get('success', False)
        async_success = results['async_mode'].get('success', False)
        
        if sync_success and async_success:
            logger.info("🎉 性能对比测试完成")
            return 0
        else:
            logger.warning("⚠️ 部分测试失败")
            return 1
            
    except Exception as e:
        logger.error(f"💥 性能对比测试失败: {str(e)}")
        return 1

if __name__ == '__main__':
    exit(main())
