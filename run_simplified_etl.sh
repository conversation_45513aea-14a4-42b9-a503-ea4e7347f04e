#!/bin/bash

# 精简ETL处理器启动脚本
# 使用方法: ./run_simplified_etl.sh [dev|prod] [test|run]

set -e  # 遇到错误立即退出

# 默认参数
ENV=${1:-dev}
MODE=${2:-run}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [[ "$ENV" != "dev" && "$ENV" != "prod" ]]; then
    log_error "无效的环境参数: $ENV"
    log_info "使用方法: $0 [dev|prod] [test|run]"
    exit 1
fi

if [[ "$MODE" != "test" && "$MODE" != "run" ]]; then
    log_error "无效的模式参数: $MODE"
    log_info "使用方法: $0 [dev|prod] [test|run]"
    exit 1
fi

# 检查Python环境
if ! command -v python &> /dev/null; then
    log_error "Python未安装或不在PATH中"
    exit 1
fi

# 检查必要的Python包
log_info "检查Python依赖..."
python -c "import pymysql, argparse, logging, datetime, time" 2>/dev/null || {
    log_error "缺少必要的Python包，请安装: pip install pymysql"
    exit 1
}

# 检查脚本文件
if [[ "$MODE" == "test" ]]; then
    SCRIPT_FILE="test_simplified_etl.py"
else
    SCRIPT_FILE="simplified_etl_processor.py"
fi

if [[ ! -f "$SCRIPT_FILE" ]]; then
    log_error "脚本文件不存在: $SCRIPT_FILE"
    exit 1
fi

# 创建日志目录
LOG_DIR="logs"
if [[ ! -d "$LOG_DIR" ]]; then
    log_info "创建日志目录: $LOG_DIR"
    mkdir -p "$LOG_DIR"
fi

# 显示执行信息
echo "=================================="
log_info "精简ETL处理器启动"
echo "=================================="
log_info "环境: $ENV"
log_info "模式: $MODE"
log_info "脚本: $SCRIPT_FILE"
log_info "日志目录: $LOG_DIR"
log_info "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo "=================================="

# 执行脚本
if [[ "$MODE" == "test" ]]; then
    log_info "开始执行测试..."
    python "$SCRIPT_FILE" "$ENV"
    TEST_EXIT_CODE=$?
    
    echo ""
    echo "=================================="
    if [[ $TEST_EXIT_CODE -eq 0 ]]; then
        log_success "所有测试通过！"
        log_info "可以安全执行ETL任务: $0 $ENV run"
    elif [[ $TEST_EXIT_CODE -eq 1 ]]; then
        log_warning "部分测试失败，请检查后再执行ETL任务"
    else
        log_error "多个测试失败，请修复问题后重试"
    fi
    echo "=================================="
    
    exit $TEST_EXIT_CODE
    
else
    log_info "开始执行ETL任务..."
    
    # 记录开始时间
    START_TIME=$(date +%s)
    
    # 执行ETL任务
    python "$SCRIPT_FILE" --env "$ENV" --log-level INFO
    ETL_EXIT_CODE=$?
    
    # 计算执行时间
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    DURATION_MIN=$((DURATION / 60))
    DURATION_SEC=$((DURATION % 60))
    
    echo ""
    echo "=================================="
    log_info "执行完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
    log_info "总执行时间: ${DURATION_MIN}分${DURATION_SEC}秒"
    
    if [[ $ETL_EXIT_CODE -eq 0 ]]; then
        log_success "ETL任务执行成功！"
        log_info "请检查日志文件确认数据处理结果"
    else
        log_error "ETL任务执行失败！"
        log_info "请查看日志文件排查问题"
    fi
    echo "=================================="
    
    # 显示日志文件位置
    if [[ -f "$LOG_DIR/simplified_etl_processor.log" ]]; then
        log_info "日志文件: $LOG_DIR/simplified_etl_processor.log"
        log_info "查看最新日志: tail -f $LOG_DIR/simplified_etl_processor.log"
    fi
    
    exit $ETL_EXIT_CODE
fi
