#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
表结构迁移脚本 V2：解决StarRocks PRIMARY KEY表的分区限制

方案：使用复合主键 (path, cre_dt) 来满足分区要求
"""

import sys
import os
import time
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

class TableMigrationManagerV2:
    """表结构迁移管理器 V2"""
    
    def __init__(self, env='dev'):
        self.env = env
        self.processor = SupersetDWETLProcessor(env=env)
        self.backup_table_name = None
        
    def backup_original_table(self):
        """备份原表"""
        print("📋 备份原表...")
        
        # 生成带时间戳的备份表名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_table_name = f"dwd_asset_file_details_backup_{timestamp}"
        
        backup_sql = f"ALTER TABLE dwd_asset_file_details RENAME {backup_table_name}"
        
        try:
            self.processor.cursor.execute(backup_sql)
            self.processor.conn.commit()
            print(f"✅ 原表已备份为: {backup_table_name}")
            self.backup_table_name = backup_table_name
            return True
        except Exception as e:
            print(f"❌ 备份原表失败: {str(e)}")
            return False

    def create_primary_key_table(self):
        """创建新的PRIMARY KEY表（复合主键方案）"""
        print("📋 创建新的PRIMARY KEY表（复合主键方案）...")
        
        create_sql = """
        CREATE TABLE `dwd_asset_file_details` (
          `path` varchar(65533) NOT NULL COMMENT "文件唯一路径",
          `cre_dt` datetime NOT NULL COMMENT "文件创建时间",
          `file_size` bigint(20) NULL COMMENT "文件大小 (Bytes)",
          `domain` varchar(255) NULL COMMENT "所属领域",
          `dataset_name` varchar(255) NULL COMMENT "所属数据集名称",
          `source_table` varchar(50) NULL COMMENT "数据来源表"
        ) ENGINE=OLAP 
        PRIMARY KEY(`path`, `cre_dt`)
        PARTITION BY time_slice(cre_dt, 1, 'day', 'floor')
        DISTRIBUTED BY HASH(`path`) BUCKETS 128 
        PROPERTIES (
        "replication_num" = "3",
        "in_memory" = "false",
        "enable_persistent_index" = "false",
        "replicated_storage" = "true",
        "storage_medium" = "HDD",
        "compression" = "LZ4"
        );
        """
        
        try:
            self.processor.cursor.execute(create_sql)
            self.processor.conn.commit()
            print("✅ PRIMARY KEY表创建成功（复合主键：path + cre_dt）")
            return True
        except Exception as e:
            print(f"❌ 创建表失败: {str(e)}")
            return False
    
    def migrate_data_with_dedup(self):
        """从备份表迁移数据并去重"""
        print("📊 开始数据迁移和去重...")
        
        if not self.backup_table_name:
            print("❌ 备份表名未找到，无法迁移数据")
            return False
        
        # 先检查备份表数据量
        try:
            count_sql = f"SELECT COUNT(*) as total_count, COUNT(DISTINCT path) as unique_paths FROM {self.backup_table_name}"
            self.processor.cursor.execute(count_sql)
            result = self.processor.cursor.fetchone()
            total_count = result[0] if result else 0
            unique_paths = result[1] if result else 0
        except Exception as e:
            print(f"❌ 查询备份表数据失败: {str(e)}")
            return False
        
        print(f"📈 备份表统计:")
        print(f"   总记录数: {total_count:,}")
        print(f"   唯一path数: {unique_paths:,}")
        print(f"   重复记录数: {total_count - unique_paths:,}")
        
        # 对于复合主键，我们需要保留每个path的最新记录，并过滤无效时间值
        migrate_sql = f"""
        INSERT INTO dwd_asset_file_details
        WITH cleaned_data AS (
            SELECT
                path,
                CASE
                    WHEN cre_dt IS NULL OR cre_dt < '0001-01-01 00:00:00'
                    THEN '2025-01-01 00:00:00'  -- 默认时间
                    ELSE cre_dt
                END as cre_dt,
                file_size, domain, dataset_name, source_table
            FROM {self.backup_table_name}
            WHERE path IS NOT NULL
        ),
        deduplicated_data AS (
            SELECT
                path, cre_dt, file_size, domain, dataset_name, source_table
            FROM (
                SELECT
                    *,
                    ROW_NUMBER() OVER(PARTITION BY path ORDER BY cre_dt DESC) as rn
                FROM cleaned_data
            ) t
            WHERE rn = 1
        )
        SELECT * FROM deduplicated_data
        """
        
        try:
            start_time = time.time()
            self.processor.cursor.execute(migrate_sql)
            self.processor.conn.commit()
            duration = time.time() - start_time
            
            print(f"✅ 数据迁移完成，耗时: {duration:.2f}秒")
            return True
        except Exception as e:
            print(f"❌ 数据迁移失败: {str(e)}")
            return False
    
    def validate_migration(self):
        """验证迁移结果"""
        print("🔍 验证迁移结果...")
        
        if not self.backup_table_name:
            print("❌ 备份表名未找到，无法验证")
            return False
        
        # 检查新表记录数
        new_count_sql = "SELECT COUNT(*) FROM dwd_asset_file_details"
        self.processor.cursor.execute(new_count_sql)
        new_count = self.processor.cursor.fetchone()[0]
        
        # 检查备份表唯一path数
        orig_unique_sql = f"SELECT COUNT(DISTINCT path) FROM {self.backup_table_name}"
        self.processor.cursor.execute(orig_unique_sql)
        orig_unique = self.processor.cursor.fetchone()[0]
        
        print(f"📊 验证结果:")
        print(f"   新表记录数: {new_count:,}")
        print(f"   备份表唯一path数: {orig_unique:,}")
        
        if new_count == orig_unique:
            print("✅ 迁移验证通过")
            return True
        else:
            print("❌ 迁移验证失败")
            return False
    
    def test_native_merge(self):
        """测试原生MERGE功能"""
        print("🧪 测试原生MERGE功能...")
        
        # 创建测试数据
        test_sql = """
        INSERT INTO dwd_asset_file_details
        VALUES ('/test/merge_test.txt', '2025-07-25 10:00:00', 1024, 'test', 'test_dataset', 'test_source')
        ON DUPLICATE KEY UPDATE
            file_size = VALUES(file_size),
            domain = VALUES(domain),
            dataset_name = VALUES(dataset_name),
            source_table = VALUES(source_table)
        """
        
        try:
            self.processor.cursor.execute(test_sql)
            self.processor.conn.commit()
            print("✅ 原生MERGE测试成功")
            
            # 清理测试数据
            cleanup_sql = "DELETE FROM dwd_asset_file_details WHERE path = '/test/merge_test.txt'"
            self.processor.cursor.execute(cleanup_sql)
            self.processor.conn.commit()
            
            return True
        except Exception as e:
            print(f"❌ 原生MERGE测试失败: {str(e)}")
            return False
    
    def rollback_migration(self):
        """回滚迁移（恢复原表）"""
        print("🔄 回滚迁移...")
        
        if not self.backup_table_name:
            print("❌ 备份表名未找到，无法回滚")
            return False
        
        try:
            # 删除新创建的表
            drop_sql = "DROP TABLE IF EXISTS dwd_asset_file_details"
            self.processor.cursor.execute(drop_sql)
            
            # 恢复原表名
            restore_sql = f"ALTER TABLE {self.backup_table_name} RENAME dwd_asset_file_details"
            self.processor.cursor.execute(restore_sql)
            
            self.processor.conn.commit()
            print("✅ 迁移已回滚，原表已恢复")
            return True
        except Exception as e:
            print(f"❌ 回滚失败: {str(e)}")
            return False

def main():
    parser = argparse.ArgumentParser(description='表结构迁移 V2：复合主键方案')
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev', help='运行环境')
    parser.add_argument('--dry-run', action='store_true', help='仅验证，不执行实际迁移')
    parser.add_argument('--skip-validation', action='store_true', help='跳过验证步骤')
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("🚀 表结构迁移 V2：复合主键方案 (path + cre_dt)")
    print("=" * 80)
    print(f"🏗️ 运行环境: {args.env}")
    print(f"🔍 DRY RUN: {'是' if args.dry_run else '否'}")
    
    if args.env == 'prod':
        confirm = input("\n⚠️ 这是生产环境迁移，请确认是否继续？(yes/no): ")
        if confirm.lower() != 'yes':
            print("❌ 迁移已取消")
            return
    
    # 创建迁移管理器
    migrator = TableMigrationManagerV2(env=args.env)
    
    try:
        # 连接数据库
        migrator.processor.connect_db()
        
        if args.dry_run:
            print("\n🔍 DRY RUN 模式 - 仅显示迁移计划")
            print("📋 迁移步骤:")
            print("   1. 备份原表 -> dwd_asset_file_details_backup_YYYYMMDD_HHMMSS")
            print("   2. 创建PRIMARY KEY表（复合主键：path + cre_dt）")
            print("   3. 迁移数据并去重（每个path保留最新记录）")
            print("   4. 验证数据完整性")
            print("   5. 测试原生MERGE功能")
        else:
            print("\n📋 开始迁移流程...")
            
            # 步骤1：备份原表
            if not migrator.backup_original_table():
                print("❌ 备份原表失败，迁移终止")
                return
            
            # 步骤2：创建新的PRIMARY KEY表
            if not migrator.create_primary_key_table():
                print("❌ 创建新表失败，开始回滚...")
                migrator.rollback_migration()
                return
            
            # 步骤3：迁移数据
            if not migrator.migrate_data_with_dedup():
                print("❌ 数据迁移失败，开始回滚...")
                migrator.rollback_migration()
                return
            
            # 步骤4：验证迁移
            if not args.skip_validation:
                if not migrator.validate_migration():
                    print("❌ 迁移验证失败，开始回滚...")
                    migrator.rollback_migration()
                    return
            
            # 步骤5：测试MERGE功能
            if not migrator.test_native_merge():
                print("⚠️ MERGE功能测试失败，但迁移已完成")
                print("📋 可能需要检查表结构或权限")
            
            print("\n🎉 迁移完成！")
            print(f"📋 原表已备份为: {migrator.backup_table_name}")
            print("📋 新表使用复合主键: (path, cre_dt)")
            print("📋 现在可以使用 native_merge 策略:")
            print("   python etl_processor.py --incremental-strategy native_merge")
    
    except Exception as e:
        print(f"💥 迁移过程中发生错误: {str(e)}")
    
    finally:
        migrator.processor.close_db()

if __name__ == "__main__":
    main()
