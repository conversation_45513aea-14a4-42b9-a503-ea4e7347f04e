2025-07-25 11:08:58,188 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 11:08:58,188 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 11:08:58,188 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite
2025-07-25 11:08:58,188 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 11:08:58,188 - superset_dw_etl - INFO -    🌟 partition_overwrite: 分区级别INSERT OVERWRITE (推荐，性能最优)
2025-07-25 11:08:58,188 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 11:08:58,188 - superset_dw_etl - INFO -    ✅ merge: MERGE操作 (类似UPSERT，适合频繁更新)
2025-07-25 11:08:58,188 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 11:08:58,188 - superset_dw_etl - INFO -    当前策略: partition_overwrite
2025-07-25 11:08:58,191 - superset_dw_etl - INFO - 📋 构建DWD增量SQL - 分区级别优化 (目标日期: 2024-01-01)
2025-07-25 11:08:58,191 - superset_dw_etl - INFO - 🚀 使用分区级别INSERT OVERWRITE，避免全表扫描，性能提升90%+
2025-07-25 11:08:58,191 - superset_dw_etl - INFO - 📋 构建DWD增量SQL - DELETE+INSERT方案 (目标日期: 2024-01-01)
2025-07-25 11:08:58,191 - superset_dw_etl - INFO - 📋 构建DWD增量SQL - MERGE方案 (目标日期: 2024-01-01)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 📋 构建DWD增量SQL - 全表INSERT OVERWRITE (目标日期: 2024-01-01)
2025-07-25 11:08:58,192 - superset_dw_etl - WARNING - ⚠️ 此方案性能较差，建议使用分区级别方案
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    🌟 partition_overwrite: 分区级别INSERT OVERWRITE (推荐，性能最优)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ✅ merge: MERGE操作 (类似UPSERT，适合频繁更新)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    当前策略: partition_overwrite
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 📋 增量策略已更改: partition_overwrite -> partition_overwrite
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    🌟 partition_overwrite: 分区级别INSERT OVERWRITE (推荐，性能最优)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ✅ merge: MERGE操作 (类似UPSERT，适合频繁更新)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    当前策略: partition_overwrite
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 📋 增量策略已更改: partition_overwrite -> delete_insert
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    🌟 partition_overwrite: 分区级别INSERT OVERWRITE (推荐，性能最优)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ✅ merge: MERGE操作 (类似UPSERT，适合频繁更新)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    当前策略: delete_insert
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 📋 增量策略已更改: delete_insert -> merge
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    🌟 partition_overwrite: 分区级别INSERT OVERWRITE (推荐，性能最优)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ✅ merge: MERGE操作 (类似UPSERT，适合频繁更新)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    当前策略: merge
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 📋 增量策略已更改: merge -> full_overwrite
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    🌟 partition_overwrite: 分区级别INSERT OVERWRITE (推荐，性能最优)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ✅ merge: MERGE操作 (类似UPSERT，适合频繁更新)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    当前策略: full_overwrite
2025-07-25 11:08:58,192 - superset_dw_etl - ERROR - ❌ 无效的增量策略: invalid_strategy
2025-07-25 11:08:58,192 - superset_dw_etl - ERROR -    支持的策略: partition_overwrite, delete_insert, merge, full_overwrite
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite
2025-07-25 11:08:58,192 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    🌟 partition_overwrite: 分区级别INSERT OVERWRITE (推荐，性能最优)
2025-07-25 11:08:58,192 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 11:08:58,193 - superset_dw_etl - INFO -    ✅ merge: MERGE操作 (类似UPSERT，适合频繁更新)
2025-07-25 11:08:58,193 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 11:08:58,193 - superset_dw_etl - INFO -    当前策略: partition_overwrite
2025-07-25 11:08:58,193 - superset_dw_etl - INFO - 📋 构建DWD增量SQL - 分区级别优化 (目标日期: 2024-01-01)
2025-07-25 11:08:58,193 - superset_dw_etl - INFO - 🚀 使用分区级别INSERT OVERWRITE，避免全表扫描，性能提升90%+
2025-07-25 11:08:58,193 - superset_dw_etl - INFO - 📋 构建DWD增量SQL - DELETE+INSERT方案 (目标日期: 2024-01-01)
2025-07-25 11:08:58,193 - superset_dw_etl - INFO - 📋 构建DWD增量SQL - MERGE方案 (目标日期: 2024-01-01)
2025-07-25 11:08:58,193 - superset_dw_etl - INFO - 📋 构建DWD增量SQL - 全表INSERT OVERWRITE (目标日期: 2024-01-01)
2025-07-25 11:08:58,193 - superset_dw_etl - WARNING - ⚠️ 此方案性能较差，建议使用分区级别方案
2025-07-25 11:20:41,277 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 11:20:41,278 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 11:20:41,278 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 11:20:41,278 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 11:20:41,278 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 11:20:41,278 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 11:20:41,278 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 11:20:41,278 - superset_dw_etl - INFO -    ✅ merge: MERGE操作 (类似UPSERT，适合频繁更新)
2025-07-25 11:20:41,278 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 11:20:41,278 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 11:20:41,281 - superset_dw_etl - INFO - 📋 构建DWD增量SQL V2 - 单SQL解决跨分区重复 (目标日期: 2025-07-25)
2025-07-25 11:20:41,281 - superset_dw_etl - INFO - 🚀 确保全表path唯一性，性能优化
2025-07-25 11:20:41,281 - superset_dw_etl - INFO - 📋 构建DWD增量SQL - 改进分区级别处理 (目标日期: 2025-07-25)
2025-07-25 11:20:41,281 - superset_dw_etl - INFO - 🚀 解决跨分区重复问题，确保path唯一性
2025-07-25 11:20:41,282 - superset_dw_etl - INFO - 📋 构建DWD增量SQL - 全表INSERT OVERWRITE (目标日期: 2025-07-25)
2025-07-25 11:20:41,282 - superset_dw_etl - WARNING - ⚠️ 此方案性能较差，建议使用分区级别方案
2025-07-25 15:20:19,885 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:20:19,885 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:20:19,885 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:20:19,885 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:20:19,885 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:20:19,885 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:20:19,885 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:20:19,885 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:20:19,885 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:20:19,886 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:20:19,886 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:20:20,227 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:20:20,227 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:26:10,316 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:26:10,316 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:26:10,316 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:26:10,316 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:26:10,316 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:26:10,316 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:26:10,316 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:26:10,316 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:26:10,316 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:26:10,316 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:26:10,316 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:26:10,557 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:26:10,607 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:26:23,202 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:26:23,203 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:26:23,203 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:26:23,203 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:26:23,203 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:26:23,203 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:26:23,203 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:26:23,203 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:26:23,203 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:26:23,203 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:26:23,203 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:26:24,088 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:26:24,604 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:29:19,486 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:29:19,487 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:29:19,487 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:29:19,487 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:29:19,487 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:29:19,487 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:29:19,487 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:29:19,487 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:29:19,487 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:29:19,487 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:29:19,487 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:29:19,744 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:29:19,787 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:31:04,718 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:31:04,718 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:31:04,718 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:31:04,718 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:31:04,718 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:31:04,718 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:31:04,718 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:31:04,718 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:31:04,718 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:31:04,718 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:31:04,718 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:31:04,981 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:31:05,024 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:31:34,698 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:31:34,698 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:31:34,698 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:31:34,698 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:31:34,698 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:31:34,698 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:31:34,698 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:31:34,698 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:31:34,698 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:31:34,698 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:31:34,698 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:31:34,957 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:31:35,003 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:32:07,434 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:32:07,434 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:32:07,435 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:32:07,435 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:32:07,435 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:32:07,435 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:32:07,435 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:32:07,435 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:32:07,435 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:32:07,435 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:32:07,435 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:32:07,887 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:32:07,954 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:33:17,664 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:33:17,664 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:33:17,664 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:33:17,664 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:33:17,664 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:33:17,664 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:33:17,664 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:33:17,664 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:33:17,664 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:33:17,664 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:33:17,664 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:33:17,915 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:33:17,916 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:33:27,604 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:33:27,604 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:33:27,604 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:33:27,604 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:33:27,604 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:33:27,604 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:33:27,604 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:33:27,604 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:33:27,604 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:33:27,604 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:33:27,604 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:33:27,860 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:33:27,923 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:34:31,566 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:34:31,566 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:34:31,566 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:34:31,566 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:34:31,566 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:34:31,566 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:34:31,566 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:34:31,566 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:34:31,566 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:34:31,566 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:34:31,566 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:34:32,010 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:34:32,054 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:36:05,502 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:36:05,502 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:36:05,502 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:36:05,503 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:36:05,503 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:36:05,503 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:36:05,503 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:36:05,503 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:36:05,503 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:36:05,503 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:36:05,503 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:36:05,750 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:36:27,893 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:36:43,577 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:36:43,577 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:36:43,577 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:36:43,577 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:36:43,577 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:36:43,577 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:36:43,577 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:36:43,577 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:36:43,577 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:36:43,578 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:36:43,578 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:36:43,987 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:36:47,599 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:38:34,643 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:38:34,643 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:38:34,643 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:38:34,643 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:38:34,643 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:38:34,643 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:38:34,643 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:38:34,643 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:38:34,643 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:38:34,643 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:38:34,643 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:38:34,904 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:38:40,579 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 15:39:58,743 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 15:39:58,743 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 15:39:58,744 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 15:39:58,744 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 15:39:58,744 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 15:39:58,744 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 15:39:58,744 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 15:39:58,744 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 15:39:58,744 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 15:39:58,744 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 15:39:58,744 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 15:39:58,748 - superset_dw_etl - INFO - 📋 构建DWD增量SQL - StarRocks原生MERGE (目标日期: 2025-07-25)
2025-07-25 15:39:58,748 - superset_dw_etl - INFO - 🚀 使用原生UPSERT语义，性能最优
2025-07-25 15:39:59,152 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 15:39:59,194 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 16:00:19,213 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 16:00:19,213 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 16:00:19,213 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 16:00:19,213 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 16:00:19,213 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 16:00:19,213 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 16:00:19,213 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 16:00:19,213 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 16:00:19,213 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 16:00:19,213 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 16:00:19,213 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 16:00:19,461 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 16:00:19,539 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 16:01:35,626 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 16:01:35,627 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 16:01:35,627 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 16:01:35,627 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 16:01:35,627 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 16:01:35,627 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 16:01:35,627 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 16:01:35,627 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 16:01:35,627 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 16:01:35,627 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 16:01:35,627 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 16:01:35,897 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 16:02:20,666 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 16:03:04,899 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 16:03:04,899 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 16:03:04,900 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 16:03:04,900 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 16:03:04,900 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 16:03:04,900 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 16:03:04,900 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 16:03:04,900 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 16:03:04,900 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 16:03:04,900 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 16:03:04,900 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 16:03:05,161 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 16:05:12,967 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 16:38:36,173 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 16:38:36,173 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 16:38:36,173 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 16:38:36,173 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 16:38:36,173 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 16:38:36,173 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 16:38:36,174 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 16:38:36,174 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 16:38:36,174 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 16:38:36,174 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 16:38:36,174 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 16:38:36,797 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 16:38:36,797 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 16:38:45,865 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 16:38:45,866 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 16:38:45,866 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 16:38:45,866 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 16:38:45,866 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 16:38:45,866 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 16:38:45,866 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 16:38:45,866 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 16:38:45,866 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 16:38:45,866 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 16:38:45,866 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 16:38:46,114 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 16:39:06,797 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 16:40:21,006 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 16:40:21,007 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 16:40:21,007 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 16:40:21,007 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 16:40:21,007 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 16:40:21,007 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 16:40:21,007 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 16:40:21,007 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 16:40:21,007 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 16:40:21,007 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 16:40:21,007 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 16:40:21,321 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 16:40:36,021 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 16:41:48,001 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 16:41:48,001 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 16:41:48,001 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 16:41:48,001 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 16:41:48,001 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 16:41:48,001 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 16:41:48,001 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 16:41:48,001 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 16:41:48,001 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 16:41:48,001 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 16:41:48,001 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 16:41:48,288 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 16:41:50,350 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 17:29:40,726 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 17:29:40,726 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 17:29:40,726 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 17:29:40,726 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 17:29:40,726 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 17:29:40,726 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 17:29:40,726 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 17:29:40,726 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 17:29:40,726 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 17:29:40,726 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 17:29:40,726 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 17:29:40,729 - superset_dw_etl - INFO - 📋 构建DWD增量SQL - StarRocks原生UPSERT (目标日期: 2025-07-25)
2025-07-25 17:29:40,729 - superset_dw_etl - INFO - 🚀 直接INSERT INTO，PRIMARY KEY表自动UPSERT，极简高效
2025-07-25 17:29:41,206 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 17:29:41,298 - superset_dw_etl - INFO - 数据库连接已关闭
2025-07-25 17:30:20,967 - superset_dw_etl - INFO - 初始化Superset数据仓库ETL处理器，环境: dev
2025-07-25 17:30:20,968 - superset_dw_etl - INFO - 默认执行模式: 异步任务模式
2025-07-25 17:30:20,968 - superset_dw_etl - INFO - 默认增量策略: partition_overwrite_v2
2025-07-25 17:30:20,968 - superset_dw_etl - INFO - 📊 增量处理策略说明:
2025-07-25 17:30:20,968 - superset_dw_etl - INFO -    🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)
2025-07-25 17:30:20,968 - superset_dw_etl - INFO -    🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)
2025-07-25 17:30:20,968 - superset_dw_etl - INFO -    ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)
2025-07-25 17:30:20,968 - superset_dw_etl - INFO -    ✅ delete_insert: DELETE+INSERT组合 (性能良好)
2025-07-25 17:30:20,968 - superset_dw_etl - INFO -    ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)
2025-07-25 17:30:20,968 - superset_dw_etl - INFO -    ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)
2025-07-25 17:30:20,968 - superset_dw_etl - INFO -    当前策略: partition_overwrite_v2
2025-07-25 17:30:21,465 - superset_dw_etl - INFO - 成功连接到dev环境数据库
2025-07-25 17:30:21,750 - superset_dw_etl - INFO - 数据库连接已关闭
