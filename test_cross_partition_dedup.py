#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
跨分区去重测试脚本

测试不同增量策略对跨分区重复数据的处理能力
"""

import sys
import os
import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def test_cross_partition_scenarios():
    """测试跨分区重复场景"""
    print("=" * 80)
    print("🧪 跨分区重复数据处理测试")
    print("=" * 80)
    
    # 模拟场景
    scenarios = [
        {
            'name': '场景1：同一文件在不同日期都有记录',
            'description': 'path=/data/file1.txt 在 2025-05-12 和 2025-07-25 都有记录',
            'expected': '只保留最新日期(2025-07-25)的记录'
        },
        {
            'name': '场景2：文件被重新扫描',
            'description': '文件在历史日期已存在，当日重新扫描发现',
            'expected': '更新为当日最新信息'
        },
        {
            'name': '场景3：文件路径标准化',
            'description': '同一文件的不同路径表示形式',
            'expected': '统一为标准路径格式'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 {scenario['name']}")
        print(f"   描述: {scenario['description']}")
        print(f"   期望: {scenario['expected']}")

def analyze_strategy_dedup_capability():
    """分析不同策略的去重能力"""
    print("\n" + "=" * 80)
    print("📊 策略去重能力分析")
    print("=" * 80)
    
    strategies = {
        'partition_overwrite_v2': {
            '跨分区去重': '✅ 支持',
            '性能影响': '中等 (需要扫描全表)',
            '数据一致性': '🌟 优秀',
            '推荐场景': '生产环境，要求数据唯一性'
        },
        'partition_overwrite': {
            '跨分区去重': '❌ 不支持',
            '性能影响': '低 (只扫描当日分区)',
            '数据一致性': '⚠️ 可能重复',
            '推荐场景': '性能优先，可接受重复数据'
        },
        'delete_insert': {
            '跨分区去重': '✅ 支持',
            '性能影响': '中等',
            '数据一致性': '✅ 良好',
            '推荐场景': '数据修复，逻辑清晰'
        },
        'merge': {
            '跨分区去重': '✅ 支持',
            '性能影响': '中等',
            '数据一致性': '✅ 良好',
            '推荐场景': '频繁更新场景'
        },
        'full_overwrite': {
            '跨分区去重': '✅ 支持',
            '性能影响': '极高 (全表扫描)',
            '数据一致性': '🌟 优秀',
            '推荐场景': '仅作备选方案'
        }
    }
    
    print(f"{'策略':<20} {'跨分区去重':<12} {'性能影响':<15} {'数据一致性':<12}")
    print("-" * 80)
    
    for strategy, capabilities in strategies.items():
        print(f"{strategy:<20} {capabilities['跨分区去重']:<12} {capabilities['性能影响']:<15} {capabilities['数据一致性']:<12}")
    
    print("\n📋 详细说明:")
    for strategy, capabilities in strategies.items():
        print(f"\n🔹 {strategy}:")
        for key, value in capabilities.items():
            print(f"   {key}: {value}")

def generate_sql_comparison():
    """生成SQL对比"""
    print("\n" + "=" * 80)
    print("🔍 SQL策略对比")
    print("=" * 80)
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.target_date = '2025-07-25'
    
    strategies = [
        ('partition_overwrite_v2', '单SQL解决跨分区重复'),
        ('partition_overwrite', '分区级别INSERT OVERWRITE'),
        ('full_overwrite', '全表INSERT OVERWRITE')
    ]
    
    for strategy, description in strategies:
        print(f"\n📋 {strategy} ({description})")
        print("-" * 60)
        
        try:
            if strategy == 'partition_overwrite_v2':
                sql = processor.build_dwd_incremental_sql_v2()
            elif strategy == 'partition_overwrite':
                sql = processor.build_dwd_incremental_sql()
            elif strategy == 'full_overwrite':
                sql = processor.build_dwd_incremental_sql_full_overwrite()
            
            # 分析SQL特征
            has_cross_partition_logic = 'LEFT JOIN' in sql.upper() and 'IS NULL' in sql.upper()
            has_global_dedup = 'ROW_NUMBER() OVER(PARTITION BY path' in sql
            
            print(f"✅ SQL生成成功")
            print(f"📏 SQL长度: {len(sql)} 字符")
            print(f"🔍 跨分区处理: {'✅ 有' if has_cross_partition_logic else '❌ 无'}")
            print(f"🎯 全局去重: {'✅ 有' if has_global_dedup else '❌ 无'}")
            
        except Exception as e:
            print(f"❌ SQL生成失败: {str(e)}")

def simulate_dws_layer_impact():
    """模拟DWS层影响"""
    print("\n" + "=" * 80)
    print("📈 DWS层影响分析")
    print("=" * 80)
    
    print("🔍 当前问题:")
    print("   如果DWD层存在跨分区重复数据，DWS层查询会受到以下影响：")
    print("   1. 统计结果不准确 (同一文件被重复计算)")
    print("   2. 存储量计算错误 (文件大小被重复累加)")
    print("   3. 趋势分析偏差 (历史数据被重复统计)")
    
    print("\n💡 解决方案:")
    print("   方案1: DWD层确保唯一性 (推荐)")
    print("   - 使用 partition_overwrite_v2 策略")
    print("   - 在数据源头解决重复问题")
    print("   - DWS层可以直接使用，无需额外去重")
    
    print("\n   方案2: DWS层去重处理")
    print("   - DWD层允许重复数据存在")
    print("   - DWS层查询时进行去重")
    print("   - 示例SQL:")
    
    dws_dedup_sql = """
    -- DWS层去重示例
    WITH deduplicated_dwd AS (
        SELECT 
            path, file_size, domain, dataset_name,
            ROW_NUMBER() OVER(PARTITION BY path ORDER BY cre_dt DESC) as rn
        FROM dwd_asset_file_details
    )
    SELECT 
        domain,
        COUNT(DISTINCT path) as file_count,
        SUM(file_size) / POWER(1024, 4) as total_size_tb
    FROM deduplicated_dwd 
    WHERE rn = 1
    GROUP BY domain
    """
    
    print(f"   {dws_dedup_sql}")
    
    print("\n📊 性能对比:")
    print("   方案1 (DWD层去重): DWS查询性能更好，逻辑更清晰")
    print("   方案2 (DWS层去重): 每次DWS查询都需要去重，性能较差")

def main():
    """主函数"""
    print("🚀 跨分区重复数据处理分析")
    
    test_cross_partition_scenarios()
    analyze_strategy_dedup_capability()
    generate_sql_comparison()
    simulate_dws_layer_impact()
    
    print("\n" + "=" * 80)
    print("📋 总结与建议")
    print("=" * 80)
    print("1. 🌟 推荐使用 partition_overwrite_v2 策略")
    print("2. ✅ 在DWD层解决跨分区重复问题")
    print("3. 🎯 确保每个path在全表中唯一")
    print("4. 📈 DWS层可以直接使用，无需额外去重")
    print("5. ⚡ 性能和数据一致性的最佳平衡")

if __name__ == "__main__":
    main()
