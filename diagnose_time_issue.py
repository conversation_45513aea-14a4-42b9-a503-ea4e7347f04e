#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
诊断时间字段问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def diagnose_time_issue():
    """诊断时间字段问题"""
    print("🔍 诊断时间字段问题...")
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.connect_db()
    
    backup_table = "dwd_asset_file_details_backup_20250725_152624"
    
    try:
        # 1. 检查NULL值
        null_check_sql = f"""
        SELECT COUNT(*) as null_count
        FROM {backup_table}
        WHERE cre_dt IS NULL
        """
        processor.cursor.execute(null_check_sql)
        null_count = processor.cursor.fetchone()[0]
        print(f"📊 NULL时间值数量: {null_count}")
        
        # 2. 检查无效时间值
        invalid_check_sql = f"""
        SELECT COUNT(*) as invalid_count
        FROM {backup_table}
        WHERE cre_dt < '0001-01-01 00:00:00'
        """
        try:
            processor.cursor.execute(invalid_check_sql)
            invalid_count = processor.cursor.fetchone()[0]
            print(f"📊 无效时间值数量: {invalid_count}")
        except Exception as e:
            print(f"⚠️ 无法检查无效时间值: {str(e)}")
        
        # 3. 检查时间范围
        range_check_sql = f"""
        SELECT 
            MIN(cre_dt) as min_time,
            MAX(cre_dt) as max_time,
            COUNT(*) as total_count
        FROM {backup_table}
        WHERE cre_dt IS NOT NULL
        """
        processor.cursor.execute(range_check_sql)
        result = processor.cursor.fetchone()
        if result:
            print(f"📊 时间范围统计:")
            print(f"   最小时间: {result[0]}")
            print(f"   最大时间: {result[1]}")
            print(f"   有效记录数: {result[2]}")
        
        # 4. 检查具体的问题记录
        problem_check_sql = f"""
        SELECT cre_dt, COUNT(*) as count
        FROM {backup_table}
        WHERE cre_dt IS NULL OR cre_dt = '0000-00-00 00:00:00'
        GROUP BY cre_dt
        LIMIT 10
        """
        try:
            processor.cursor.execute(problem_check_sql)
            problems = processor.cursor.fetchall()
            if problems:
                print(f"📊 问题时间值:")
                for problem in problems:
                    print(f"   时间值: {problem[0]}, 数量: {problem[1]}")
            else:
                print("✅ 未发现明显的问题时间值")
        except Exception as e:
            print(f"⚠️ 检查问题记录失败: {str(e)}")
        
        # 5. 采样检查前几条记录
        sample_sql = f"""
        SELECT path, cre_dt, file_size
        FROM {backup_table}
        LIMIT 5
        """
        processor.cursor.execute(sample_sql)
        samples = processor.cursor.fetchall()
        print(f"📊 数据样本:")
        for sample in samples:
            print(f"   路径: {sample[0][:50]}..., 时间: {sample[1]}, 大小: {sample[2]}")
            
    except Exception as e:
        print(f"❌ 诊断失败: {str(e)}")
    
    finally:
        processor.close_db()

if __name__ == "__main__":
    diagnose_time_issue()
