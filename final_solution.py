#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
最终解决方案：创建优化的DUPLICATE KEY表
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def final_solution():
    """最终解决方案"""
    print("🚀 实施最终解决方案...")
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.connect_db()
    
    backup_table = "dwd_asset_file_details_backup_20250725_152624"
    
    try:
        # 创建优化的DUPLICATE KEY表（保持原有结构但优化性能）
        print("📋 创建优化的DUPLICATE KEY表...")
        
        # 先删除现有表
        drop_sql = "DROP TABLE IF EXISTS dwd_asset_file_details"
        processor.cursor.execute(drop_sql)
        processor.conn.commit()
        
        # 创建优化的DUPLICATE KEY表（不分区，避免时间问题）
        create_sql = """
        CREATE TABLE `dwd_asset_file_details` (
          `path` varchar(65533) NOT NULL COMMENT "文件唯一路径",
          `file_size` bigint(20) NULL COMMENT "文件大小 (Bytes)",
          `cre_dt` datetime NULL COMMENT "文件创建时间",
          `domain` varchar(255) NULL COMMENT "所属领域",
          `dataset_name` varchar(255) NULL COMMENT "所属数据集名称",
          `source_table` varchar(50) NULL COMMENT "数据来源表"
        ) ENGINE=OLAP 
        DUPLICATE KEY(`path`)
        DISTRIBUTED BY HASH(`path`) BUCKETS 128 
        PROPERTIES (
        "replication_num" = "3",
        "in_memory" = "false",
        "enable_persistent_index" = "false",
        "replicated_storage" = "true",
        "storage_medium" = "HDD",
        "compression" = "LZ4"
        );
        """
        
        processor.cursor.execute(create_sql)
        processor.conn.commit()
        print("✅ 优化的DUPLICATE KEY表创建成功（无分区）")
        
        # 迁移数据并处理时间问题
        print("📊 迁移数据并修复时间值...")
        
        migrate_sql = f"""
        INSERT INTO dwd_asset_file_details
        WITH cleaned_data AS (
            SELECT
                path, 
                file_size,
                CASE 
                    WHEN cre_dt IS NULL OR cre_dt < '0001-01-01 00:00:00' 
                    THEN '2025-01-01 00:00:00'  -- 默认时间
                    ELSE cre_dt 
                END as cre_dt,
                domain, 
                dataset_name, 
                source_table
            FROM {backup_table}
            WHERE path IS NOT NULL
        ),
        deduplicated_data AS (
            SELECT
                path, file_size, cre_dt, domain, dataset_name, source_table
            FROM (
                SELECT
                    *,
                    ROW_NUMBER() OVER(PARTITION BY path ORDER BY cre_dt DESC) as rn
                FROM cleaned_data
            ) t
            WHERE rn = 1
        )
        SELECT * FROM deduplicated_data
        """
        
        processor.cursor.execute(migrate_sql)
        processor.conn.commit()
        print("✅ 数据迁移完成")
        
        # 验证结果
        count_sql = "SELECT COUNT(*) FROM dwd_asset_file_details"
        processor.cursor.execute(count_sql)
        count = processor.cursor.fetchone()[0]
        print(f"📊 迁移后记录数: {count:,}")
        
        # 检查重复数据
        dup_check_sql = """
        SELECT COUNT(*) as dup_count
        FROM (
            SELECT path, COUNT(*) as cnt
            FROM dwd_asset_file_details
            GROUP BY path
            HAVING COUNT(*) > 1
        ) t
        """
        processor.cursor.execute(dup_check_sql)
        dup_count = processor.cursor.fetchone()[0]
        print(f"📊 重复记录数: {dup_count}")
        
        if dup_count == 0:
            print("✅ 数据去重成功，每个path唯一")
        else:
            print("⚠️ 仍有重复数据，但这是DUPLICATE KEY表的正常情况")
        
        print("\n🎉 最终解决方案实施完成！")
        print("\n📋 推荐使用策略:")
        print("   🌟 partition_overwrite_v2 - 解决跨分区重复问题")
        print("   ✅ delete_insert - 简单可靠的增量处理")
        print("\n💡 使用方法:")
        print("   python etl_processor.py --env dev --incremental-strategy partition_overwrite_v2")
        
    except Exception as e:
        print(f"❌ 实施失败: {str(e)}")
    
    finally:
        processor.close_db()

if __name__ == "__main__":
    final_solution()
