#!/bin/bash

# 构建和推送Docker镜像脚本
# 使用方法: ./build_and_push.sh [版本号]

set -e

# 默认版本号
VERSION=${1:-latest}

# 镜像配置
REGISTRY="harbor.internal.sais.com.cn"
PROJECT="assets-platform"
IMAGE_NAME="superset-dw-etl"
FULL_IMAGE_NAME="$REGISTRY/$PROJECT/$IMAGE_NAME:$VERSION"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "开始构建Docker镜像: $FULL_IMAGE_NAME"

# 检查必要文件是否存在
if [[ ! -f "Dockerfile" ]]; then
    log_error "Dockerfile 不存在"
    exit 1
fi

if [[ ! -f "etl_processor.py" ]]; then
    log_error "etl_processor.py 不存在"
    exit 1
fi

if [[ ! -f "requirements.txt" ]]; then
    log_error "requirements.txt 不存在"
    exit 1
fi

# 检查Docker是否可用
if ! command -v docker &> /dev/null; then
    log_error "Docker 命令不可用，请确保已安装Docker"
    exit 1
fi

# 构建镜像
log_info "构建Docker镜像..."
docker build -t "$FULL_IMAGE_NAME" .

if [[ $? -eq 0 ]]; then
    log_success "镜像构建成功: $FULL_IMAGE_NAME"
else
    log_error "镜像构建失败"
    exit 1
fi

# 推送镜像
log_info "推送镜像到Harbor仓库..."
docker push "$FULL_IMAGE_NAME"

if [[ $? -eq 0 ]]; then
    log_success "镜像推送成功: $FULL_IMAGE_NAME"
else
    log_error "镜像推送失败"
    exit 1
fi

# 显示镜像信息
log_info "镜像信息:"
docker images | grep "$IMAGE_NAME" | head -5

log_success "构建和推送完成！"

# 显示使用说明
echo ""
log_info "使用说明:"
echo "  在K8s配置文件中使用: $FULL_IMAGE_NAME"
echo "  本地运行测试: docker run --rm $FULL_IMAGE_NAME python etl_processor.py --env dev --debug"
echo "  查看镜像详情: docker inspect $FULL_IMAGE_NAME"
