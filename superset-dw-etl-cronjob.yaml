apiVersion: batch/v1
kind: CronJob
metadata:
  name: superset-dw-etl-job
  namespace: data-assets-prod-ns  # 根据实际情况修改
spec:
  schedule: "0 10 * * *"  # 每天上午10点执行
  concurrencyPolicy: Forbid  # 不允许并发执行
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: superset-dw-etl
            image: harbor-paas.internal.sais.com.cn/assets-platform/python:3.11-slim
            workingDir: /app
            command:
            - /bin/sh
            - -c
            - |
              pip install -r requirements.txt && \
              python etl_processor.py --env prod --log-level INFO
            volumeMounts:
              - mountPath: /app
                name: app-volume
            env:
            - name: TZ
              value: "Asia/Shanghai"
            resources:
              requests:
                memory: "512Mi"
                cpu: "500m"
              limits:
                memory: "2Gi"
                cpu: "2000m"
          volumes:
            - name: app-volume
              configMap:
                name: superset-dw-etl-files
                defaultMode: 0755
          restartPolicy: OnFailure
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: superset-dw-etl-files
  namespace: data-assets-prod-ns  # 根据实际情况修改
data:
  etl_processor.py: |
    # 这里需要将etl_processor.py的内容复制过来
    # 或者使用kubectl create configmap命令创建
  requirements.txt: |
    pymysql==1.1.0
