#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
import datetime
import logging
import pymysql
import argparse
import shutil
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Tuple, Optional

# 创建日志目录 - 适配本地和容器环境
if os.path.exists('/app'):
    LOG_DIR = '/app/logs/superset_dw_etl'
else:
    LOG_DIR = os.path.join(os.getcwd(), 'logs')

LOG_FILE = 'etl_processor.log'
LOG_PATH = os.path.join(LOG_DIR, LOG_FILE)

# 确保日志目录存在
os.makedirs(LOG_DIR, exist_ok=True)

# 检查是否存在当天的日志文件，如果存在则删除
today = datetime.datetime.now().strftime('%Y%m%d')
dated_log_file = f'etl_processor-{today}.log'
dated_log_path = os.path.join(LOG_DIR, dated_log_file)

if os.path.exists(dated_log_path):
    try:
        os.remove(dated_log_path)
        print(f"删除已存在的当天日志文件: {dated_log_path}")
    except Exception as e:
        print(f"删除当天日志文件失败: {str(e)}")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOG_PATH)
    ]
)
logger = logging.getLogger('superset_dw_etl')

# 数据库配置
DB_CONFIG = {
    'dev': {
        'host': '************',
        'port': 30000,
        'user': 'root',
        'password': '',
        'db': 'CRAWLER'
    },
    'prod': {
        'host': 'kube-starrocks-fe-search.data-infra-prod-ns',
        'port': 9030,
        'user': 'root',
        'password': '',
        'db': 'CRAWLER'
    }
}

def rename_log_file():
    """将当前日志文件重命名为带日期的格式"""
    try:
        today = datetime.datetime.now().strftime('%Y%m%d')
        dated_log_file = f'etl_processor-{today}.log'
        dated_log_path = os.path.join(LOG_DIR, dated_log_file)
        
        # 如果目标文件已存在，先删除
        if os.path.exists(dated_log_path):
            os.remove(dated_log_path)
            logger.info(f"删除已存在的日期日志文件: {dated_log_path}")
        
        # 重命名当前日志文件
        if os.path.exists(LOG_PATH):
            shutil.copy2(LOG_PATH, dated_log_path)
            logger.info(f"日志文件已复制: {LOG_PATH} -> {dated_log_path}")

            # 复制完成后删除原始日志文件
            os.remove(LOG_PATH)
            logger.info(f"原始日志文件已删除: {LOG_PATH}")
        else:
            logger.warning(f"当前日志文件不存在: {LOG_PATH}")
    except Exception as e:
        logger.error(f"重命名日志文件失败: {str(e)}")

class SupersetDWETLProcessor:
    def __init__(self, env='dev'):
        self.env = env
        self.db_config = DB_CONFIG[env]
        self.conn = None
        self.cursor = None

        # 可配置的重试参数
        self.max_retries = 5
        self.retry_delay = 30

        # 执行模式配置
        self.use_async_mode = True  # 默认使用异步模式
        self.use_dws_parallel = False  # 默认DWS层使用串行模式
        self.init_mode = False  # 默认增量模式
        self.target_date = None  # 目标处理日期，None表示当日

        # 增量处理策略配置
        self.incremental_strategy = 'partition_overwrite_v2'  # 默认使用改进的分区级别处理
        # 可选策略: 'partition_overwrite_v2', 'native_merge', 'partition_overwrite', 'delete_insert', 'merge', 'full_overwrite'

        # 任务执行统计
        self.task_stats = {
            'dwd_success': False,
            'dws_daily_success': False,
            'dws_weekly_success': False,
            'dws_domain_success': False,
            'start_time': None,
            'end_time': None,
            'total_duration': 0,
            'async_tasks_submitted': 0,
            'async_tasks_completed': 0
        }

        logger.info(f"初始化Superset数据仓库ETL处理器，环境: {env}")
        logger.info(f"默认执行模式: 异步任务模式")
        logger.info(f"默认增量策略: {self.incremental_strategy}")
        self.log_incremental_strategies()
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.conn = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['db'],
                charset='utf8mb4',
                autocommit=False  # 手动控制事务
            )
            self.cursor = self.conn.cursor(pymysql.cursors.DictCursor)
            logger.info(f"成功连接到{self.env}环境数据库")
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
    
    def close_db(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        logger.info("数据库连接已关闭")

    def set_incremental_strategy(self, strategy: str):
        """设置增量处理策略"""
        valid_strategies = ['partition_overwrite_v2', 'native_merge', 'partition_overwrite', 'delete_insert', 'merge', 'full_overwrite']
        if strategy not in valid_strategies:
            logger.error(f"❌ 无效的增量策略: {strategy}")
            logger.error(f"   支持的策略: {', '.join(valid_strategies)}")
            raise ValueError(f"不支持的增量策略: {strategy}")

        old_strategy = self.incremental_strategy
        self.incremental_strategy = strategy
        logger.info(f"📋 增量策略已更改: {old_strategy} -> {strategy}")
        self.log_incremental_strategies()

    def log_incremental_strategies(self):
        """输出增量策略说明"""
        logger.info("📊 增量处理策略说明:")
        logger.info("   🌟 partition_overwrite_v2: 单SQL解决跨分区重复 (推荐，确保path唯一)")
        logger.info("   🚀 native_merge: StarRocks原生MERGE (需要PRIMARY KEY表，性能最优)")
        logger.info("   ✅ partition_overwrite: 分区级别INSERT OVERWRITE (性能优，但可能有重复)")
        logger.info("   ✅ delete_insert: DELETE+INSERT组合 (性能良好)")
        logger.info("   ✅ merge: 模拟MERGE操作 (适用于DUPLICATE KEY表)")
        logger.info("   ❌ full_overwrite: 全表INSERT OVERWRITE (不推荐，性能差)")
        logger.info(f"   当前策略: {self.incremental_strategy}")
    
    def generate_task_name(self, table_name: str) -> str:
        """生成唯一的任务名称"""
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]  # 精确到毫秒
        task_name = f"{table_name}_{timestamp}"
        return task_name

    def submit_async_task(self, sql: str, task_name: str, description: str, max_retries: int = 3, query_timeout: int = 3600) -> bool:
        """提交异步任务到StarRocks"""
        for attempt in range(max_retries):
            try:
                logger.info(f"🚀 提交异步任务: {task_name} ({description})")
                logger.info(f"   查询超时设置: {query_timeout}秒")

                # 构建SUBMIT TASK语句，包含超时设置
                submit_sql = f"SUBMIT /*+set_var(query_timeout={query_timeout})*/ TASK {task_name} AS {sql}"

                logger.debug(f"执行SQL: {submit_sql}")

                start_time = time.time()
                self.cursor.execute(submit_sql)
                self.conn.commit()

                duration = time.time() - start_time
                logger.info(f"✅ 任务 {task_name} 提交成功，耗时: {duration:.2f}秒")

                # 立即检查任务是否成功提交到队列
                if self.verify_task_submitted(task_name):
                    logger.info(f"✅ 任务 {task_name} 已成功加入执行队列")
                    return True
                else:
                    logger.warning(f"⚠️ 任务 {task_name} 提交后未在队列中找到")
                    return False

            except Exception as e:
                logger.error(f"❌ 提交任务 {task_name} 失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if self.conn:
                    self.conn.rollback()

                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 3  # 增加等待时间
                    logger.info(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"💥 任务 {task_name} 最终提交失败")
                    return False

        return False

    def verify_task_submitted(self, task_name: str) -> bool:
        """验证任务是否成功提交到队列"""
        try:
            # 检查任务是否在tasks表中
            check_sql = f"""
            SELECT TASK_NAME, CREATE_TIME, DEFINITION
            FROM information_schema.tasks
            WHERE TASK_NAME = '{task_name}'
            """

            self.cursor.execute(check_sql)
            result = self.cursor.fetchone()

            if result:
                logger.info(f"📋 任务 {task_name} 已在任务表中注册")
                logger.info(f"   创建时间: {result.get('CREATE_TIME', 'N/A')}")
                return True
            else:
                logger.warning(f"⚠️ 任务 {task_name} 未在任务表中找到")
                return False

        except Exception as e:
            logger.error(f"❌ 验证任务提交状态失败: {str(e)}")
            return False

    def wait_for_task_completion(self, task_name: str, description: str, max_wait_seconds: int = 3600) -> bool:
        """等待异步任务完成，支持智能监控和详细状态报告"""
        logger.info(f"⏳ 等待任务完成: {task_name} ({description})")
        logger.info(f"   最大等待时间: {max_wait_seconds}秒 ({max_wait_seconds/60:.1f}分钟)")

        check_interval = 15  # 每15秒检查一次，减少数据库压力
        total_waited = 0
        last_status = None
        last_progress = None
        status_change_count = 0

        # 记录开始监控时间
        monitor_start_time = time.time()

        while total_waited < max_wait_seconds:
            try:
                # 查询任务状态，分步获取信息避免语法错误
                status_sql = f"""
                SELECT
                    TASK_NAME,
                    STATE,
                    CREATE_TIME,
                    FINISH_TIME,
                    PROGRESS
                FROM information_schema.task_runs
                WHERE TASK_NAME = '{task_name}'
                ORDER BY CREATE_TIME DESC
                LIMIT 1
                """

                self.cursor.execute(status_sql)
                result = self.cursor.fetchone()

                if not result:
                    # 如果是刚开始监控，任务可能还没有出现在task_runs中
                    if total_waited < 30:
                        logger.info(f"⏳ 任务 {task_name} 正在初始化，等待出现在执行队列中...")
                    else:
                        logger.warning(f"⚠️ 未找到任务 {task_name} 的执行记录，可能任务提交失败")
                    time.sleep(check_interval)
                    total_waited += check_interval
                    continue

                current_status = result['STATE']
                progress = result.get('PROGRESS', '')
                create_time = result.get('CREATE_TIME', '')
                finish_time = result.get('FINISH_TIME', '')

                # 如果任务失败，单独查询错误信息
                error_message = ''
                query_id = ''
                database = ''
                error_code = 0

                if current_status == 'FAILED':
                    try:
                        error_sql = f"""
                        SELECT ERROR_MESSAGE, QUERY_ID, DATABASE, ERROR_CODE
                        FROM information_schema.task_runs
                        WHERE TASK_NAME = '{task_name}'
                        ORDER BY CREATE_TIME DESC
                        LIMIT 1
                        """
                        self.cursor.execute(error_sql)
                        error_result = self.cursor.fetchone()
                        if error_result:
                            error_message = error_result.get('ERROR_MESSAGE', '')
                            query_id = error_result.get('QUERY_ID', '')
                            database = error_result.get('DATABASE', '')
                            error_code = error_result.get('ERROR_CODE', 0)
                    except Exception as e:
                        logger.warning(f"⚠️ 获取错误详情失败: {str(e)}")

                # 状态变化或进度变化时记录日志
                status_changed = current_status != last_status
                progress_changed = progress != last_progress

                if status_changed or progress_changed:
                    status_change_count += 1
                    elapsed_time = time.time() - monitor_start_time

                    logger.info(f"📊 任务 {task_name} 状态更新 (第{status_change_count}次)")
                    logger.info(f"   当前状态: {current_status}")
                    if progress and progress != '0%':
                        logger.info(f"   执行进度: {progress}")
                    logger.info(f"   监控时长: {elapsed_time:.1f}秒")
                    if query_id:
                        logger.info(f"   查询ID: {query_id}")

                    last_status = current_status
                    last_progress = progress

                # 检查任务状态
                if current_status == 'SUCCESS':
                    total_duration = time.time() - monitor_start_time
                    logger.info(f"🎉 任务 {task_name} 执行成功！")
                    logger.info(f"   创建时间: {create_time}")
                    logger.info(f"   完成时间: {finish_time}")
                    logger.info(f"   总监控时长: {total_duration:.1f}秒")
                    logger.info(f"   数据库: {database}")
                    return True

                elif current_status == 'FAILED':
                    total_duration = time.time() - monitor_start_time
                    logger.error(f"💥 任务 {task_name} 执行失败！")
                    logger.error(f"   错误代码: {error_code}")
                    logger.error(f"   错误信息: {error_message}")
                    logger.error(f"   创建时间: {create_time}")
                    logger.error(f"   失败时间: {finish_time}")
                    logger.error(f"   总监控时长: {total_duration:.1f}秒")
                    if query_id:
                        logger.error(f"   查询ID: {query_id} (可用于进一步调试)")
                    return False

                elif current_status in ['PENDING', 'RUNNING']:
                    # 任务仍在执行中，定期输出心跳信息
                    if total_waited % 60 == 0 and total_waited > 0:  # 每分钟输出一次心跳
                        logger.info(f"💓 任务 {task_name} 仍在执行中 (已等待 {total_waited//60} 分钟)")
                        if progress and progress != '0%':
                            logger.info(f"   当前进度: {progress}")

                    time.sleep(check_interval)
                    total_waited += check_interval

                elif current_status == 'MERGED':
                    logger.info(f"🔄 任务 {task_name} 已被合并到其他任务中")
                    time.sleep(check_interval)
                    total_waited += check_interval

                else:
                    logger.warning(f"❓ 任务 {task_name} 状态未知: {current_status}")
                    time.sleep(check_interval)
                    total_waited += check_interval

            except Exception as e:
                logger.error(f"❌ 查询任务 {task_name} 状态失败: {str(e)}")
                time.sleep(check_interval)
                total_waited += check_interval

        # 超时处理
        total_duration = time.time() - monitor_start_time
        logger.error(f"⏰ 任务 {task_name} 等待超时！")
        logger.error(f"   设定超时时间: {max_wait_seconds}秒 ({max_wait_seconds/60:.1f}分钟)")
        logger.error(f"   实际等待时间: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)")
        logger.error(f"   最后状态: {last_status or '未知'}")
        logger.error(f"   建议: 检查集群负载情况，考虑增加超时时间或优化SQL查询")
        return False

    def execute_async_task(self, sql: str, table_name: str, description: str,
                          max_retries: int = None, query_timeout: int = 3600,
                          max_wait_seconds: int = 7200) -> bool:
        """执行异步SQL任务的完整流程：提交异步任务 -> 等待完成 -> 验证数据"""
        if max_retries is None:
            max_retries = self.max_retries

        logger.info(f"🚀 开始执行异步SQL任务: {description}")
        logger.info(f"   目标表: {table_name}")
        logger.info(f"   最大重试次数: {max_retries}")
        logger.info(f"   查询超时: {query_timeout}秒 ({query_timeout/60:.1f}分钟)")
        logger.info(f"   最大等待时间: {max_wait_seconds}秒 ({max_wait_seconds/60:.1f}分钟)")

        # 生成唯一任务名称
        task_name = self.generate_task_name(table_name)

        # 第一步：提交异步任务
        logger.info(f"📋 第一步：提交异步任务")
        task_submitted = self.submit_async_task(
            sql=sql,
            task_name=task_name,
            description=description,
            max_retries=max_retries,
            query_timeout=query_timeout
        )

        if not task_submitted:
            logger.error(f"💥 异步任务提交失败: {description}")
            return False

        # 第二步：等待任务完成
        logger.info(f"⏳ 第二步：等待任务执行完成")
        task_completed = self.wait_for_task_completion(
            task_name=task_name,
            description=description,
            max_wait_seconds=max_wait_seconds
        )

        if not task_completed:
            logger.error(f"💥 异步任务执行失败或超时: {description}")
            # 尝试获取更多错误信息
            self.log_task_failure_details(task_name)
            return False

        # 第三步：验证数据完整性
        logger.info(f"🔍 第三步：验证数据完整性")
        if not self.verify_table_data(table_name, expected_min_rows=1):
            logger.error(f"❌ 数据验证失败: {description}")
            return False

        logger.info(f"🎉 异步SQL任务完成: {description}")
        return True

    def log_task_failure_details(self, task_name: str):
        """记录任务失败的详细信息，用于调试"""
        try:
            # 获取任务的详细失败信息
            detail_sql = f"""
            SELECT
                TASK_NAME,
                STATE,
                ERROR_CODE,
                ERROR_MESSAGE,
                CREATE_TIME,
                FINISH_TIME,
                QUERY_ID,
                DEFINITION
            FROM information_schema.task_runs
            WHERE TASK_NAME = '{task_name}'
            ORDER BY CREATE_TIME DESC
            LIMIT 1
            """

            self.cursor.execute(detail_sql)
            result = self.cursor.fetchone()

            if result:
                logger.error(f"📋 任务失败详情:")
                logger.error(f"   任务名称: {result.get('TASK_NAME', 'N/A')}")
                logger.error(f"   最终状态: {result.get('STATE', 'N/A')}")
                logger.error(f"   错误代码: {result.get('ERROR_CODE', 'N/A')}")
                logger.error(f"   错误信息: {result.get('ERROR_MESSAGE', 'N/A')}")
                logger.error(f"   创建时间: {result.get('CREATE_TIME', 'N/A')}")
                logger.error(f"   完成时间: {result.get('FINISH_TIME', 'N/A')}")
                logger.error(f"   查询ID: {result.get('QUERY_ID', 'N/A')}")

                # 截取SQL定义的前200个字符用于日志
                definition = result.get('DEFINITION', '')
                if definition:
                    definition_preview = definition[:200] + "..." if len(definition) > 200 else definition
                    logger.error(f"   SQL定义: {definition_preview}")
            else:
                logger.error(f"❌ 无法获取任务 {task_name} 的失败详情")

        except Exception as e:
            logger.error(f"❌ 获取任务失败详情时出错: {str(e)}")

    def get_all_async_tasks_status(self) -> Dict[str, any]:
        """获取所有异步任务的状态概览"""
        try:
            # 使用简化的查询，避免复杂字段组合导致的语法错误
            yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')

            # 分步查询，先获取基本信息
            basic_sql = f"""
            SELECT
                TASK_NAME,
                STATE,
                CREATE_TIME
            FROM information_schema.task_runs
            WHERE CREATE_TIME >= '{yesterday}'
            ORDER BY CREATE_TIME DESC
            """

            self.cursor.execute(basic_sql)
            basic_results = self.cursor.fetchall()

            # 统计各种状态的任务数量
            status_summary = {
                'total_tasks': len(basic_results),
                'success_tasks': 0,
                'failed_tasks': 0,
                'running_tasks': 0,
                'pending_tasks': 0,
                'other_tasks': 0,
                'tasks_detail': []
            }

            # 处理基本结果
            for result in basic_results:
                state = result.get('STATE', 'UNKNOWN')
                task_name = result.get('TASK_NAME', '')
                create_time = result.get('CREATE_TIME', '')

                # 对于每个任务，尝试获取更详细的信息
                task_detail = {
                    'task_name': task_name,
                    'state': state,
                    'create_time': create_time,
                    'finish_time': '',
                    'progress': '',
                    'error_message': '',
                    'database': ''
                }

                # 尝试获取详细信息（如果失败就使用基本信息）
                try:
                    detail_sql = f"""
                    SELECT FINISH_TIME, PROGRESS
                    FROM information_schema.task_runs
                    WHERE TASK_NAME = '{task_name}'
                    ORDER BY CREATE_TIME DESC
                    LIMIT 1
                    """
                    self.cursor.execute(detail_sql)
                    detail_result = self.cursor.fetchone()

                    if detail_result:
                        task_detail['finish_time'] = detail_result.get('FINISH_TIME', '')
                        task_detail['progress'] = detail_result.get('PROGRESS', '')

                except Exception as detail_e:
                    # 如果获取详细信息失败，继续使用基本信息
                    logger.debug(f"获取任务 {task_name} 详细信息失败: {str(detail_e)}")

                status_summary['tasks_detail'].append(task_detail)

                # 统计状态
                if state == 'SUCCESS':
                    status_summary['success_tasks'] += 1
                elif state == 'FAILED':
                    status_summary['failed_tasks'] += 1
                elif state == 'RUNNING':
                    status_summary['running_tasks'] += 1
                elif state == 'PENDING':
                    status_summary['pending_tasks'] += 1
                else:
                    status_summary['other_tasks'] += 1

            return status_summary

        except Exception as e:
            logger.error(f"❌ 获取异步任务状态概览失败: {str(e)}")
            return {
                'total_tasks': 0,
                'success_tasks': 0,
                'failed_tasks': 0,
                'running_tasks': 0,
                'pending_tasks': 0,
                'other_tasks': 0,
                'tasks_detail': [],
                'error': str(e)
            }

    def log_async_tasks_summary(self):
        """记录异步任务状态摘要"""
        logger.info("📊 异步任务状态摘要:")

        status_summary = self.get_all_async_tasks_status()

        if 'error' in status_summary:
            logger.error(f"❌ 无法获取任务状态摘要: {status_summary['error']}")
            return

        total = status_summary['total_tasks']
        if total == 0:
            logger.info("   📋 近24小时内无异步任务记录")
            return

        logger.info(f"   📋 总任务数: {total}")
        logger.info(f"   ✅ 成功任务: {status_summary['success_tasks']} ({status_summary['success_tasks']/total*100:.1f}%)")
        logger.info(f"   ❌ 失败任务: {status_summary['failed_tasks']} ({status_summary['failed_tasks']/total*100:.1f}%)")
        logger.info(f"   🔄 运行中任务: {status_summary['running_tasks']}")
        logger.info(f"   ⏳ 等待中任务: {status_summary['pending_tasks']}")

        if status_summary['other_tasks'] > 0:
            logger.info(f"   ❓ 其他状态任务: {status_summary['other_tasks']}")

        # 显示最近的几个任务详情
        recent_tasks = status_summary['tasks_detail'][:5]  # 显示最近5个任务
        if recent_tasks:
            logger.info("   📝 最近任务详情:")
            for task in recent_tasks:
                state_icon = {
                    'SUCCESS': '✅',
                    'FAILED': '❌',
                    'RUNNING': '🔄',
                    'PENDING': '⏳'
                }.get(task['state'], '❓')

                logger.info(f"     {state_icon} {task['task_name']}: {task['state']}")
                if task['progress'] and task['progress'] != '0%':
                    logger.info(f"        进度: {task['progress']}")
                if task['error_message']:
                    logger.info(f"        错误: {task['error_message'][:100]}...")  # 截取前100字符

    def execute_sql_task(self, sql: str, table_name: str, description: str, max_retries: int = None) -> bool:
        """执行同步SQL任务的完整流程：执行SQL -> 验证数据 (保留用于兼容性)"""
        if max_retries is None:
            max_retries = self.max_retries

        logger.info(f"🔄 开始执行同步SQL任务: {description}")
        logger.info(f"目标表: {table_name}")
        logger.info(f"最大重试次数: {max_retries}")

        # 执行SQL
        for attempt in range(max_retries):
            try:
                logger.info(f"执行SQL ({attempt + 1}/{max_retries}): {description}")
                start_time = time.time()

                self.cursor.execute(sql)
                affected_rows = self.cursor.rowcount
                self.conn.commit()

                duration = time.time() - start_time
                logger.info(f"SQL执行成功，影响行数: {affected_rows}, 耗时: {duration:.2f}秒")
                break

            except Exception as e:
                logger.error(f"SQL执行失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if self.conn:
                    self.conn.rollback()

                if attempt < max_retries - 1:
                    # 对于集群负载问题，使用更长的等待时间
                    if "Too many create partition requests" in str(e) or "RPC failure" in str(e):
                        wait_time = (attempt + 1) * self.retry_delay
                        logger.info(f"🔄 检测到集群负载问题，等待 {wait_time} 秒后重试...")
                        logger.info(f"💡 建议：可以在非高峰期执行ETL任务以避免集群负载问题")
                    else:
                        wait_time = (attempt + 1) * 5  # 5秒、10秒、15秒、20秒
                        logger.info(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"SQL执行最终失败: {description}")
                    return False

        # 验证数据完整性
        if not self.verify_table_data(table_name, expected_min_rows=1):
            logger.error(f"❌ 数据验证失败: {description}")
            return False

        logger.info(f"✅ 同步SQL任务完成: {description}")
        return True

    def verify_table_data(self, table_name: str, expected_min_rows: int = 0) -> bool:
        """验证表数据是否正确写入，根据表类型使用不同的验证策略"""
        # 对于DWD表，我们不强制要求最小行数，因为数据可能为空
        if table_name == "dwd_asset_file_details":
            expected_min_rows = 0

        try:
            # 根据表类型选择合适的时间字段
            time_field_map = {
                'dwd_asset_file_details': 'cre_dt',
                'dws_asset_storage_trend_daily': 'stat_date',
                'dws_asset_storage_trend_weekly': 'week_start_date',
                'dws_asset_domain_distribution': None  # 领域分布表没有时间字段
            }

            time_field = time_field_map.get(table_name, 'cre_dt')  # 默认使用cre_dt

            if time_field:
                # 有时间字段的表
                verify_sql = f"""
                SELECT
                    COUNT(*) as row_count,
                    MAX({time_field}) as latest_date,
                    MIN({time_field}) as earliest_date
                FROM {table_name}
                """
            else:
                # 没有时间字段的表（如领域分布表）
                verify_sql = f"""
                SELECT COUNT(*) as row_count FROM {table_name}
                """

            self.cursor.execute(verify_sql)
            result = self.cursor.fetchone()

            if result:
                row_count = result['row_count']

                logger.info(f"表 {table_name} 验证结果:")
                logger.info(f"  总行数: {row_count:,}")

                if time_field and 'latest_date' in result:
                    latest_date = result['latest_date']
                    earliest_date = result['earliest_date']
                    logger.info(f"  最新日期: {latest_date}")
                    logger.info(f"  最早日期: {earliest_date}")

                # 对于领域分布表，显示额外的统计信息
                if table_name == 'dws_asset_domain_distribution' and row_count > 0:
                    try:
                        domain_sql = f"""
                        SELECT
                            COUNT(DISTINCT domain) as domain_count,
                            SUM(dataset_count) as total_datasets,
                            SUM(size_in_tb) as total_size_tb
                        FROM {table_name}
                        """
                        self.cursor.execute(domain_sql)
                        domain_result = self.cursor.fetchone()

                        if domain_result:
                            logger.info(f"  领域数量: {domain_result['domain_count']}")
                            logger.info(f"  数据集总数: {domain_result['total_datasets']:,}")
                            logger.info(f"  总存储量: {domain_result['total_size_tb']:.2f} TB")

                    except Exception as e:
                        logger.warning(f"领域分布统计查询失败: {str(e)}")

                if row_count >= expected_min_rows:
                    logger.info(f"表 {table_name} 数据验证通过")
                    return True
                else:
                    logger.error(f"表 {table_name} 行数不足，期望至少 {expected_min_rows} 行，实际 {row_count} 行")
                    return False
            else:
                logger.error(f"无法获取表 {table_name} 的统计信息")
                return False

        except Exception as e:
            logger.error(f"验证表 {table_name} 数据失败: {str(e)}")
            return False

    def get_target_date_str(self) -> str:
        """获取目标处理日期字符串"""
        if self.target_date:
            return self.target_date
        else:
            return datetime.datetime.now().strftime('%Y-%m-%d')

    def process_dwd_layer(self) -> bool:
        """处理DWD层数据 - 支持初始化模式和增量模式"""
        logger.info("=" * 60)
        if self.init_mode:
            logger.info("🏗️ 开始处理DWD层数据 (初始化模式 - 全量历史数据)")
        else:
            target_date = self.get_target_date_str()
            logger.info(f"🔄 开始处理DWD层数据 (增量模式 - 目标日期: {target_date})")
        logger.info("=" * 60)

        # 根据模式选择不同的处理逻辑
        if self.init_mode:
            # 初始化模式：处理全量历史数据
            logger.info("🔄 初始化模式：处理全量历史数据")
            dwd_sql = self.build_dwd_init_sql()
            description = "DWD层数据处理 (初始化模式 - 全量历史数据)"
            query_timeout = 14400  # 4小时超时，初始化数据量大
            max_wait_seconds = 18000  # 最大等待5小时
        else:
            # 增量模式：根据策略选择不同的处理方法
            target_date = self.get_target_date_str()
            logger.info(f"🔄 增量模式：处理目标日期 {target_date} 的数据")
            logger.info(f"📋 使用增量策略: {self.incremental_strategy}")

            # 根据策略选择SQL和处理逻辑
            if self.incremental_strategy == 'partition_overwrite_v2':
                # 单SQL解决跨分区重复问题 - 推荐方案
                dwd_sql = self.build_dwd_incremental_sql_v2()
                description = f"DWD层数据处理 (单SQL解决跨分区重复 - {target_date})"
                query_timeout = 2400  # 40分钟超时
                max_wait_seconds = 4200  # 最大等待70分钟

            elif self.incremental_strategy == 'native_merge':
                # StarRocks原生MERGE操作 - 性能最优
                dwd_sql = self.build_dwd_incremental_sql_native_merge()
                description = f"DWD层数据处理 (StarRocks原生MERGE - {target_date})"
                query_timeout = 1200  # 20分钟超时
                max_wait_seconds = 2400  # 最大等待40分钟

            elif self.incremental_strategy == 'partition_overwrite':
                # 分区级别INSERT OVERWRITE - 性能优但可能有重复
                logger.warning("⚠️ 此策略可能导致跨分区path重复，建议使用partition_overwrite_v2")
                dwd_sql = self.build_dwd_incremental_sql()
                description = f"DWD层数据处理 (分区级别INSERT OVERWRITE - {target_date})"
                query_timeout = 1800  # 30分钟超时
                max_wait_seconds = 3600  # 最大等待1小时

            elif self.incremental_strategy == 'delete_insert':
                # DELETE+INSERT方案
                # 先清理当日数据
                if not self.clean_target_date_data(target_date):
                    logger.error("❌ 清理目标日期数据失败，终止DWD处理")
                    return False

                dwd_sql = self.build_dwd_incremental_sql_delete_insert()
                description = f"DWD层数据处理 (DELETE+INSERT - {target_date})"
                query_timeout = 2400  # 40分钟超时
                max_wait_seconds = 4200  # 最大等待70分钟

            elif self.incremental_strategy == 'merge':
                # MERGE方案 (类似UPSERT)
                dwd_sql = self.build_dwd_incremental_sql_merge()
                description = f"DWD层数据处理 (MERGE - {target_date})"
                query_timeout = 2400  # 40分钟超时
                max_wait_seconds = 4200  # 最大等待70分钟

            elif self.incremental_strategy == 'full_overwrite':
                # 原始的全表INSERT OVERWRITE方案 (不推荐)
                logger.warning("⚠️ 使用全表INSERT OVERWRITE方案，性能较差，建议切换到分区级别方案")
                # 清理目标日期的数据（支持重跑）
                if not self.clean_target_date_data(target_date):
                    logger.error("❌ 清理目标日期数据失败，终止DWD处理")
                    return False

                dwd_sql = self.build_dwd_incremental_sql_full_overwrite()
                description = f"DWD层数据处理 (全表INSERT OVERWRITE - {target_date})"
                query_timeout = 3600  # 1小时超时
                max_wait_seconds = 5400  # 最大等待1.5小时
            else:
                logger.error(f"❌ 不支持的增量策略: {self.incremental_strategy}")
                return False

        # 执行异步DWD层SQL任务
        success = self.execute_async_task(
            sql=dwd_sql,
            table_name="dwd_asset_file_details",
            description=description,
            query_timeout=query_timeout,
            max_wait_seconds=max_wait_seconds
        )

        self.task_stats['dwd_success'] = success

        if success:
            logger.info("🎉 DWD层数据处理完成，可以开始执行DWS层")
        else:
            logger.error("💥 DWD层数据处理失败，终止ETL流程")

        return success

    def build_dwd_init_sql(self) -> str:
        """构建DWD层初始化SQL - 处理全量历史数据"""
        logger.info("📋 构建DWD初始化SQL (全量历史数据)")

        return """
        INSERT OVERWRITE dwd_asset_file_details
        WITH
        -- 1. 整合所有数据源，并标准化字段 (全量历史数据)
        all_sources_unioned AS (
            -- 来源1: tp_crawler_record (核心采集表) - 全量数据
            SELECT
                path, file_size, cre_dt,
                -- 清洗 domain 字段
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'tp_crawler_record' AS source_table,
                1 AS source_priority -- 给予最高优先级
            FROM tp_crawler_record
            WHERE path IS NOT NULL

            UNION ALL

            -- 来源2: tp_compare_crawler_282 (历史数据)
            SELECT
                path, file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'tp_compare_crawler_282' AS source_table,
                2 AS source_priority
            FROM tp_compare_crawler_282
            WHERE path IS NOT NULL

            UNION ALL

            -- 来源3: mutimodel_processed (历史数据，处理路径前缀)
            SELECT
                SUBSTRING(path, LENGTH('/cpfs01/projects-HDD/cfff-85cad58c20e7_HDD/public') + 2) AS path,
                file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'mutimodel_processed' AS source_table,
                2 AS source_priority
            FROM mutimodel_processed
            WHERE path IS NOT NULL

            UNION ALL

            -- 来源4: crawler_filescan_extra_hwy (历史数据，处理路径前缀)
            SELECT
                SUBSTRING(path, LENGTH('/cfff-4a8d9af84f66_HDD/public') + 2) AS path,
                file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'crawler_filescan_extra_hwy' AS source_table,
                2 AS source_priority
            FROM crawler_filescan_extra_hwy
            WHERE path IS NOT NULL

            UNION ALL

            -- 来源5: fuxi_scan (历史数据，处理复杂路径前缀)
            SELECT
                CASE
                    WHEN path LIKE '/cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD/public%' THEN SUBSTRING(path, LENGTH('/cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD/public') + 2)
                    WHEN path LIKE '/cpfs01/projects-HDD/cfff-01ff502a0784_HDD/public%' THEN SUBSTRING(path, LENGTH('/cpfs01/projects-HDD/cfff-01ff502a0784_HDD/public') + 2)
                    ELSE path
                END AS path,
                file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'fuxi_scan' AS source_table,
                2 AS source_priority
            FROM fuxi_scan
            WHERE path IS NOT NULL

            UNION ALL

            -- 来源6: crawler_filescan_extra (历史数据，处理路径前缀)
            SELECT
                SUBSTRING(path, LENGTH('/cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD/public') + 2) AS path,
                file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'crawler_filescan_extra' AS source_table,
                2 AS source_priority
            FROM crawler_filescan_extra
            WHERE path IS NOT NULL
        ),
        -- 2. 按 path 去重，优先保留 source_priority 高的，然后是最新的记录
        deduplicated_final AS (
            SELECT
                path,
                file_size,
                cre_dt,
                domain,
                dataset_name,
                source_table
            FROM (
                SELECT
                    *,
                    ROW_NUMBER() OVER(PARTITION BY path ORDER BY source_priority ASC, cre_dt DESC) as rn
                FROM all_sources_unioned
            ) t
            WHERE rn = 1
        )
        -- 3. 最终选择所需字段插入 DWD 表
        SELECT
            
            path,
            file_size,
            cre_dt,
            domain,
            dataset_name,
            source_table
        FROM deduplicated_final
        """

    def build_dwd_incremental_sql(self) -> str:
        """构建DWD层增量SQL - 改进的分区级别处理，解决跨分区重复问题"""
        target_date = self.get_target_date_str()
        next_date = (datetime.datetime.strptime(target_date, '%Y-%m-%d') + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        logger.info(f"📋 构建DWD增量SQL - 改进分区级别处理 (目标日期: {target_date})")
        logger.info(f"🚀 解决跨分区重复问题，确保path唯一性")

        return f"""
        -- 改进的分区级别处理 - 先删除历史重复path，再插入当日数据
        -- 第一步：删除其他分区中与当日新增数据path重复的记录
        DELETE FROM dwd_asset_file_details
        WHERE path IN (
            SELECT DISTINCT path
            FROM tp_crawler_record
            WHERE path IS NOT NULL
              AND cre_dt >= '{target_date} 00:00:00'
              AND cre_dt < '{next_date} 00:00:00'
        )
        AND NOT (cre_dt >= '{target_date} 00:00:00' AND cre_dt < '{next_date} 00:00:00');

        -- 第二步：分区级别INSERT OVERWRITE当日数据
        INSERT OVERWRITE dwd_asset_file_details
        PARTITION(cre_dt >= '{target_date} 00:00:00' AND cre_dt < '{next_date} 00:00:00')
        WITH
        -- 1. 获取当日新增数据
        daily_new_data AS (
            SELECT
                path, file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'tp_crawler_record' AS source_table,
                1 AS source_priority
            FROM tp_crawler_record
            WHERE path IS NOT NULL
              AND cre_dt >= '{target_date} 00:00:00'
              AND cre_dt < '{next_date} 00:00:00'
        ),
        -- 2. 获取当日分区的历史数据
        existing_daily_data AS (
            SELECT
                path, file_size, cre_dt, domain, dataset_name, source_table,
                2 AS source_priority
            FROM dwd_asset_file_details
            WHERE cre_dt >= '{target_date} 00:00:00'
              AND cre_dt < '{next_date} 00:00:00'
        ),
        -- 3. 合并当日数据
        all_daily_data AS (
            SELECT * FROM daily_new_data
            UNION ALL
            SELECT * FROM existing_daily_data
        ),
        -- 4. 当日分区内去重
        deduplicated_daily AS (
            SELECT
                path, file_size, cre_dt, domain, dataset_name, source_table
            FROM (
                SELECT
                    *,
                    ROW_NUMBER() OVER(
                        PARTITION BY path
                        ORDER BY source_priority ASC, cre_dt DESC
                    ) as rn
                FROM all_daily_data
            ) t
            WHERE rn = 1
        )
        SELECT * FROM deduplicated_daily
        """

    def build_dwd_incremental_sql_v2(self) -> str:
        """构建DWD层增量SQL - 适配hash_key主键表的INSERT OVERWRITE方案"""
        target_date = self.get_target_date_str()
        next_date = (datetime.datetime.strptime(target_date, '%Y-%m-%d') + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        logger.info(f"📋 构建DWD增量SQL V2 - 适配hash_key主键表 (目标日期: {target_date})")
        logger.info(f"🚀 确保全表path唯一性，生成hash_key主键")

        return f"""
        -- 适配hash_key主键表的INSERT OVERWRITE方案
        INSERT OVERWRITE dwd_asset_file_details
        WITH
        -- 1. 获取当日新增数据
        daily_new_data AS (
            SELECT
                MURMUR_HASH3_32(path) as hash_key,
                path,
                file_size,
                CASE
                    WHEN cre_dt IS NULL OR cre_dt < '0001-01-01 00:00:00'
                    THEN '2025-01-01 00:00:00'
                    ELSE cre_dt
                END as cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'tp_crawler_record' AS source_table,
                1 AS source_priority  -- 最高优先级
            FROM tp_crawler_record
            WHERE path IS NOT NULL
              AND cre_dt >= '{target_date} 00:00:00'
              AND cre_dt < '{next_date} 00:00:00'
        ),
        -- 2. 获取当日新增数据的path列表
        daily_paths AS (
            SELECT DISTINCT path FROM daily_new_data
        ),
        -- 3. 获取历史数据（排除与当日新增path重复的记录）
        historical_data_filtered AS (
            SELECT
                h.hash_key, h.path, h.file_size, h.cre_dt, h.domain, h.dataset_name, h.source_table,
                2 AS source_priority  -- 较低优先级
            FROM dwd_asset_file_details h
            LEFT JOIN daily_paths dp ON h.path = dp.path
            WHERE dp.path IS NULL  -- 排除重复path
        ),
        -- 4. 合并所有数据
        all_data_combined AS (
            -- 当日新增数据（最高优先级）
            SELECT * FROM daily_new_data

            UNION ALL

            -- 历史数据（已排除重复path）
            SELECT * FROM historical_data_filtered
        ),
        -- 5. 最终去重（理论上已经没有重复，但保险起见）
        final_deduplicated AS (
            SELECT
                hash_key, path, file_size, cre_dt, domain, dataset_name, source_table
            FROM (
                SELECT
                    *,
                    ROW_NUMBER() OVER(
                        PARTITION BY path
                        ORDER BY source_priority ASC, cre_dt DESC
                    ) as rn
                FROM all_data_combined
            ) t
            WHERE rn = 1
        )
        SELECT hash_key, path, file_size, cre_dt, domain, dataset_name, source_table
        FROM final_deduplicated
        """

    def clean_target_date_data(self, target_date: str) -> bool:
        """清理目标日期的数据，支持重跑 - 使用分区级别删除优化"""
        logger.info(f"🧹 清理目标日期数据: {target_date}")
        logger.info(f"🚀 使用分区级别删除，避免全表扫描")

        try:
            # 删除目标日期的数据 - 使用分区级别删除
            next_date = (datetime.datetime.strptime(target_date, '%Y-%m-%d') + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
            clean_sql = f"""
            DELETE FROM dwd_asset_file_details
            WHERE cre_dt >= '{target_date} 00:00:00'
              AND cre_dt < '{next_date} 00:00:00'
            """

            start_time = time.time()
            self.cursor.execute(clean_sql)
            affected_rows = self.cursor.rowcount
            self.conn.commit()

            duration = time.time() - start_time
            logger.info(f"✅ 分区级别清理完成，删除 {affected_rows} 行数据，耗时: {duration:.2f}秒")
            return True

        except Exception as e:
            logger.error(f"❌ 清理目标日期数据失败: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False

    def build_dwd_incremental_sql_delete_insert(self) -> str:
        """构建DWD层增量SQL - DELETE+INSERT方案 (备选方案)"""
        target_date = self.get_target_date_str()
        next_date = (datetime.datetime.strptime(target_date, '%Y-%m-%d') + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        logger.info(f"📋 构建DWD增量SQL - DELETE+INSERT方案 (目标日期: {target_date})")

        return f"""
        -- 方案2: DELETE+INSERT - 先删除当日数据，再插入新数据
        WITH
        -- 1. 获取当日新增数据
        daily_new_data AS (
            SELECT
                path, file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'tp_crawler_record' AS source_table
            FROM tp_crawler_record
            WHERE path IS NOT NULL
              AND cre_dt >= '{target_date} 00:00:00'
              AND cre_dt < '{next_date} 00:00:00'
        ),
        -- 2. 去重处理 (同一路径保留最新记录)
        deduplicated_new_data AS (
            SELECT
                path, file_size, cre_dt, domain, dataset_name, source_table
            FROM (
                SELECT
                    *,
                    ROW_NUMBER() OVER(PARTITION BY path ORDER BY cre_dt DESC) as rn
                FROM daily_new_data
            ) t
            WHERE rn = 1
        )
        -- 3. 插入去重后的新数据
        INSERT INTO dwd_asset_file_details
        SELECT * FROM deduplicated_new_data
        """

    def build_dwd_incremental_sql_merge(self) -> str:
        """构建DWD层增量SQL - 模拟MERGE方案 (适用于DUPLICATE KEY表)"""
        target_date = self.get_target_date_str()
        next_date = (datetime.datetime.strptime(target_date, '%Y-%m-%d') + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        logger.info(f"📋 构建DWD增量SQL - 模拟MERGE方案 (目标日期: {target_date})")
        logger.warning("⚠️ 当前表为DUPLICATE KEY类型，使用DELETE+INSERT模拟MERGE语义")

        return f"""
        -- 模拟MERGE操作：先删除重复path，再插入新数据（适用于DUPLICATE KEY表）
        -- 第一步：删除与当日新增数据path重复的历史记录
        DELETE FROM dwd_asset_file_details
        WHERE path IN (
            SELECT DISTINCT path
            FROM tp_crawler_record
            WHERE path IS NOT NULL
              AND cre_dt >= '{target_date} 00:00:00'
              AND cre_dt < '{next_date} 00:00:00'
        );

        -- 第二步：插入当日新增数据（已去重）
        INSERT INTO dwd_asset_file_details
        WITH daily_new_data AS (
            SELECT
                path, file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'tp_crawler_record' AS source_table
            FROM tp_crawler_record
            WHERE path IS NOT NULL
              AND cre_dt >= '{target_date} 00:00:00'
              AND cre_dt < '{next_date} 00:00:00'
        ),
        deduplicated_data AS (
            SELECT
                path, file_size, cre_dt, domain, dataset_name, source_table
            FROM (
                SELECT
                    *,
                    ROW_NUMBER() OVER(PARTITION BY path ORDER BY cre_dt DESC) as rn
                FROM daily_new_data
            ) t
            WHERE rn = 1
        )
        SELECT * FROM deduplicated_data
        """

    def build_dwd_incremental_sql_native_merge(self) -> str:
        """构建DWD层增量SQL - StarRocks原生UPSERT (简化版本)"""
        target_date = self.get_target_date_str()
        next_date = (datetime.datetime.strptime(target_date, '%Y-%m-%d') + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        logger.info(f"📋 构建DWD增量SQL - StarRocks原生UPSERT (目标日期: {target_date})")
        logger.info(f"🚀 直接INSERT INTO，PRIMARY KEY表自动UPSERT，极简高效")

        return f"""
        -- StarRocks PRIMARY KEY表原生UPSERT - 直接INSERT即可，自动处理重复主键
        INSERT INTO dwd_asset_file_details
        WITH deduplicated_daily AS (
            SELECT
                MURMUR_HASH3_32(path) as hash_key,
                path,
                file_size,
                CASE
                    WHEN cre_dt IS NULL OR cre_dt < '0001-01-01 00:00:00'
                    THEN '2025-01-01 00:00:00'
                    ELSE cre_dt
                END as cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'tp_crawler_record' AS source_table
            FROM (
                SELECT
                    *,
                    ROW_NUMBER() OVER(PARTITION BY path ORDER BY cre_dt DESC) as rn
                FROM tp_crawler_record
                WHERE path IS NOT NULL
                  AND cre_dt >= '{target_date} 00:00:00'
                  AND cre_dt < '{next_date} 00:00:00'
            ) t
            WHERE rn = 1
        )
        SELECT hash_key, path, file_size, cre_dt, domain, dataset_name, source_table
        FROM deduplicated_daily
        """

    def build_dwd_incremental_sql_full_overwrite(self) -> str:
        """构建DWD层增量SQL - 原始全表INSERT OVERWRITE方案 (不推荐，仅作备选)"""
        target_date = self.get_target_date_str()
        next_date = (datetime.datetime.strptime(target_date, '%Y-%m-%d') + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        logger.info(f"📋 构建DWD增量SQL - 全表INSERT OVERWRITE (目标日期: {target_date})")
        logger.warning("⚠️ 此方案性能较差，建议使用分区级别方案")

        return f"""
        INSERT OVERWRITE dwd_asset_file_details
        WITH
        -- 1. 获取历史数据 (排除目标日期的数据，避免重复)
        historical_data AS (
            SELECT
                path, file_size, cre_dt, domain, dataset_name, source_table
            FROM dwd_asset_file_details
            WHERE NOT (cre_dt >= '{target_date} 00:00:00' AND cre_dt < '{next_date} 00:00:00')
        ),
        -- 2. 获取目标日期的新增数据
        daily_new_data AS (
            SELECT
                path, file_size, cre_dt,
                CASE WHEN domain = 'lifesicence' THEN 'lifescience' ELSE domain END AS domain,
                dataset_name,
                'tp_crawler_record' AS source_table,
                1 AS source_priority
            FROM tp_crawler_record
            WHERE path IS NOT NULL
              AND cre_dt >= '{target_date} 00:00:00'
              AND cre_dt < '{next_date} 00:00:00'
        ),
        -- 3. 合并历史数据和当日新增数据
        all_data_combined AS (
            SELECT
                path, file_size, cre_dt, domain, dataset_name, source_table,
                2 AS source_priority
            FROM historical_data

            UNION ALL

            SELECT
                path, file_size, cre_dt, domain, dataset_name, source_table,
                source_priority
            FROM daily_new_data
        ),
        -- 4. 去重处理
        deduplicated_final AS (
            SELECT
                path, file_size, cre_dt, domain, dataset_name, source_table
            FROM (
                SELECT
                    *,
                    ROW_NUMBER() OVER(
                        PARTITION BY path
                        ORDER BY source_priority ASC, cre_dt DESC
                    ) as rn
                FROM all_data_combined
            ) t
            WHERE rn = 1
        )
        SELECT * FROM deduplicated_final
        """

    def process_dws_daily_trend(self) -> bool:
        """处理DWS日度趋势表 - dws_asset_storage_trend_daily (使用异步INSERT OVERWRITE)"""
        logger.info("📊 开始处理DWS日度趋势表 (异步模式)")

        # 使用INSERT OVERWRITE替代TRUNCATE + INSERT
        daily_sql = """
        INSERT OVERWRITE dws_asset_storage_trend_daily
        WITH
        daily_agg AS (
            -- 按天聚合每日新增量
            SELECT
                DATE(cre_dt) as stat_date,
                SUM(file_size) / POWER(1024, 4) as daily_storage_tb
            FROM dwd_asset_file_details
            GROUP BY DATE(cre_dt)
        ),
        cumulative_agg AS (
            -- 计算每日累计量
            SELECT
                stat_date,
                daily_storage_tb,
                SUM(daily_storage_tb) OVER (ORDER BY stat_date ASC) as cumulative_storage_tb
            FROM daily_agg
        ),
        dod_calcs AS (
            -- 计算日环比
            SELECT
                stat_date,
                daily_storage_tb,
                cumulative_storage_tb,
                LAG(daily_storage_tb, 1, 0) OVER (ORDER BY stat_date) as prev_day_storage,
                LAG(cumulative_storage_tb, 1, 0) OVER (ORDER BY stat_date) as prev_day_cumulative
            FROM cumulative_agg
        )
        SELECT
            stat_date,
            daily_storage_tb,
            cumulative_storage_tb,
            -- 计算日环比百分比，分母为0时处理
            IF(prev_day_storage = 0, 0, (daily_storage_tb - prev_day_storage) * 100.0 / prev_day_storage) AS daily_dod_pct,
            -- 计算累计量环比百分比
            IF(prev_day_cumulative = 0, 0, (cumulative_storage_tb - prev_day_cumulative) * 100.0 / prev_day_cumulative) AS cumulative_dod_pct
        FROM dod_calcs
        """

        success = self.execute_async_task(
            sql=daily_sql,
            table_name="dws_asset_storage_trend_daily",
            description="DWS日度趋势表处理 (INSERT OVERWRITE)",
            query_timeout=1800,  # 30分钟超时
            max_wait_seconds=2400  # 最大等待40分钟
        )

        self.task_stats['dws_daily_success'] = success
        return success

    def process_dws_weekly_trend(self) -> bool:
        """处理DWS周度趋势表 - dws_asset_storage_trend_weekly (使用异步INSERT OVERWRITE)"""
        logger.info("📈 开始处理DWS周度趋势表 (异步模式)")

        # 使用INSERT OVERWRITE替代TRUNCATE + INSERT
        weekly_sql = """
        INSERT OVERWRITE dws_asset_storage_trend_weekly
        WITH
        weekly_agg AS (
            -- 按周聚合每周新增量
            SELECT
                DATE_TRUNC('week', cre_dt) as week_start_date,
                SUM(file_size) / POWER(1024, 4) as weekly_storage_tb
            FROM dwd_asset_file_details
            GROUP BY DATE_TRUNC('week', cre_dt)
        ),
        cumulative_agg AS (
            -- 计算每周累计量
            SELECT
                week_start_date,
                weekly_storage_tb,
                SUM(weekly_storage_tb) OVER (ORDER BY week_start_date ASC) as cumulative_storage_tb
            FROM weekly_agg
        ),
        wow_calcs AS (
            -- 计算周环比
            SELECT
                week_start_date,
                weekly_storage_tb,
                cumulative_storage_tb,
                LAG(weekly_storage_tb, 1, 0) OVER (ORDER BY week_start_date) as prev_week_storage,
                LAG(cumulative_storage_tb, 1, 0) OVER (ORDER BY week_start_date) as prev_week_cumulative
            FROM cumulative_agg
        )
        SELECT
            week_start_date,
            weekly_storage_tb,
            cumulative_storage_tb,
            -- 计算周环比百分比
            IF(prev_week_storage = 0, 0, (weekly_storage_tb - prev_week_storage) * 100.0 / prev_week_storage) AS weekly_wow_pct,
            -- 计算累计量周环比百分比
            IF(prev_week_cumulative = 0, 0, (cumulative_storage_tb - prev_week_cumulative) * 100.0 / prev_week_cumulative) AS cumulative_wow_pct
        FROM wow_calcs
        """

        success = self.execute_async_task(
            sql=weekly_sql,
            table_name="dws_asset_storage_trend_weekly",
            description="DWS周度趋势表处理 (INSERT OVERWRITE)",
            query_timeout=1800,  # 30分钟超时
            max_wait_seconds=2400  # 最大等待40分钟
        )

        self.task_stats['dws_weekly_success'] = success
        return success

    def process_dws_domain_distribution(self) -> bool:
        """处理DWS领域分布表 - dws_asset_domain_distribution (使用异步INSERT OVERWRITE)"""
        logger.info("🌐 开始处理DWS领域分布表 (异步模式)")

        # 使用INSERT OVERWRITE替代TRUNCATE + INSERT
        domain_sql = """
        INSERT OVERWRITE dws_asset_domain_distribution
        SELECT
            domain,
            COUNT(DISTINCT dataset_name) as dataset_count,
            SUM(file_size) / POWER(1024, 4) as size_in_tb
        FROM dwd_asset_file_details
        WHERE domain IS NOT NULL
        GROUP BY domain
        """

        success = self.execute_async_task(
            sql=domain_sql,
            table_name="dws_asset_domain_distribution",
            description="DWS领域分布表处理 (INSERT OVERWRITE)",
            query_timeout=1200,  # 20分钟超时
            max_wait_seconds=1800  # 最大等待30分钟
        )

        self.task_stats['dws_domain_success'] = success
        return success

    def process_dws_layer_sequential(self) -> Dict[str, bool]:
        """串行处理DWS层所有表 (异步任务模式下推荐串行执行以避免资源竞争)"""
        logger.info("=" * 60)
        logger.info("⚡ 开始串行处理DWS层数据 (异步任务模式)")
        logger.info("=" * 60)

        # 定义DWS层处理任务
        dws_tasks = [
            ('daily_trend', self.process_dws_daily_trend, 'dws_asset_storage_trend_daily'),
            ('weekly_trend', self.process_dws_weekly_trend, 'dws_asset_storage_trend_weekly'),
            ('domain_distribution', self.process_dws_domain_distribution, 'dws_asset_domain_distribution')
        ]

        results = {}
        task_start_time = time.time()

        logger.info(f"准备串行执行 {len(dws_tasks)} 个DWS异步任务:")
        for task_name, _, table_name in dws_tasks:
            logger.info(f"  - {task_name} -> {table_name}")

        # 串行执行DWS层任务，确保任务间的依赖和资源管理
        for i, (task_name, task_func, table_name) in enumerate(dws_tasks, 1):
            logger.info(f"\n🔄 执行第 {i}/{len(dws_tasks)} 个DWS任务: {task_name}")
            logger.info(f"   目标表: {table_name}")

            task_individual_start = time.time()

            try:
                result = task_func()
                results[task_name] = result

                task_individual_duration = time.time() - task_individual_start

                if result:
                    logger.info(f"✅ DWS任务完成 [{i}/{len(dws_tasks)}]: {task_name}")
                    logger.info(f"   耗时: {task_individual_duration:.1f}秒")
                else:
                    logger.error(f"❌ DWS任务失败 [{i}/{len(dws_tasks)}]: {task_name}")
                    logger.error(f"   耗时: {task_individual_duration:.1f}秒")
                    # 可以选择是否继续执行后续任务
                    logger.warning(f"⚠️ 任务 {task_name} 失败，但继续执行后续任务")

            except Exception as e:
                results[task_name] = False
                task_individual_duration = time.time() - task_individual_start
                logger.error(f"💥 DWS任务异常 [{i}/{len(dws_tasks)}]: {task_name}")
                logger.error(f"   异常信息: {str(e)}")
                logger.error(f"   耗时: {task_individual_duration:.1f}秒")

        # 统计结果
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        total_duration = time.time() - task_start_time

        logger.info("=" * 60)
        logger.info(f"📊 DWS层串行处理完成统计:")
        logger.info(f"   总任务数: {total_count}")
        logger.info(f"   成功任务: {success_count}")
        logger.info(f"   失败任务: {total_count - success_count}")
        logger.info(f"   成功率: {(success_count/total_count)*100:.1f}%")
        logger.info(f"   总耗时: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)")

        # 详细结果
        for task_name, success in results.items():
            status_icon = "✅" if success else "❌"
            logger.info(f"   {status_icon} {task_name}: {'成功' if success else '失败'}")

        logger.info("=" * 60)

        return results

    def process_dws_layer_parallel(self) -> Dict[str, bool]:
        """并行处理DWS层所有表 (保留用于兼容性，但在异步模式下不推荐使用)"""
        logger.warning("⚠️ 并行模式在异步任务环境下可能导致资源竞争，推荐使用串行模式")
        logger.info("=" * 60)
        logger.info("🚀 开始并行处理DWS层数据")
        logger.info("=" * 60)

        # 定义DWS层处理任务
        dws_tasks = [
            ('daily_trend', self.process_dws_daily_trend, 'dws_asset_storage_trend_daily'),
            ('weekly_trend', self.process_dws_weekly_trend, 'dws_asset_storage_trend_weekly'),
            ('domain_distribution', self.process_dws_domain_distribution, 'dws_asset_domain_distribution')
        ]

        results = {}
        task_start_time = time.time()

        logger.info(f"准备并行执行 {len(dws_tasks)} 个DWS任务:")
        for task_name, _, table_name in dws_tasks:
            logger.info(f"  - {task_name} -> {table_name}")

        # 使用线程池并行执行DWS层任务
        with ThreadPoolExecutor(max_workers=3, thread_name_prefix="DWS-Worker") as executor:
            # 提交所有任务
            future_to_task = {
                executor.submit(task_func): (task_name, table_name)
                for task_name, task_func, table_name in dws_tasks
            }

            logger.info(f"已提交 {len(future_to_task)} 个并行任务到线程池")

            # 等待任务完成并收集结果
            completed_tasks = 0
            for future in as_completed(future_to_task):
                task_name, table_name = future_to_task[future]
                completed_tasks += 1

                try:
                    result = future.result()
                    results[task_name] = result

                    if result:
                        logger.info(f"✅ DWS任务完成 [{completed_tasks}/{len(dws_tasks)}]: {task_name} -> {table_name}")
                    else:
                        logger.error(f"❌ DWS任务失败 [{completed_tasks}/{len(dws_tasks)}]: {task_name} -> {table_name}")

                except Exception as e:
                    results[task_name] = False
                    logger.error(f"💥 DWS任务异常 [{completed_tasks}/{len(dws_tasks)}]: {task_name} -> {str(e)}")

        # 统计结果
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        total_duration = time.time() - task_start_time

        logger.info("=" * 60)
        logger.info(f"📊 DWS层并行处理完成统计:")
        logger.info(f"   总任务数: {total_count}")
        logger.info(f"   成功任务: {success_count}")
        logger.info(f"   失败任务: {total_count - success_count}")
        logger.info(f"   成功率: {(success_count/total_count)*100:.1f}%")
        logger.info(f"   总耗时: {total_duration:.2f}秒")

        # 详细结果
        for task_name, success in results.items():
            status_icon = "✅" if success else "❌"
            logger.info(f"   {status_icon} {task_name}: {'成功' if success else '失败'}")

        logger.info("=" * 60)

        return results

    def run(self):
        """执行完整的ETL流程"""
        self.task_stats['start_time'] = datetime.datetime.now()

        try:
            logger.info("=" * 80)
            logger.info("🎯 开始执行Superset数据仓库ETL任务")
            logger.info(f"📍 执行环境: {self.env}")
            logger.info(f"⏰ 开始时间: {self.task_stats['start_time']}")
            logger.info("=" * 80)

            # 连接数据库
            logger.info("🔌 连接数据库...")
            self.connect_db()
            logger.info("✅ 数据库连接成功")

            # 第一阶段：处理DWD层
            logger.info("\n" + "🏗️  第一阶段：处理DWD层数据")
            logger.info("📋 DWD层将整合6个数据源并进行数据清洗去重")

            dwd_start_time = time.time()
            dwd_success = self.process_dwd_layer()
            dwd_duration = time.time() - dwd_start_time

            if not dwd_success:
                logger.error("💥 DWD层处理失败，终止ETL流程")
                logger.error(f"⏱️  DWD层执行耗时: {dwd_duration:.2f}秒")
                return

            logger.info(f"🎉 DWD层处理成功，耗时: {dwd_duration:.2f}秒")
            logger.info("✅ 数据依赖满足，可以开始处理DWS层")

            # 第二阶段：处理DWS层 (根据配置选择串行或并行)
            if self.use_dws_parallel:
                logger.info("\n" + "🚀 第二阶段：并行处理DWS层数据")
                logger.info("📊 DWS层将并行处理3个汇总表：日度趋势、周度趋势、领域分布")
                logger.warning("⚠️ 并行模式在异步任务环境下可能导致资源竞争")
                dws_start_time = time.time()
                dws_results = self.process_dws_layer_parallel()
                dws_duration = time.time() - dws_start_time
            else:
                logger.info("\n" + "⚡ 第二阶段：串行处理DWS层数据 (推荐模式)")
                logger.info("📊 DWS层将串行处理3个汇总表：日度趋势、周度趋势、领域分布")
                logger.info("💡 使用串行模式避免异步任务间的资源竞争，确保执行稳定性")
                dws_start_time = time.time()
                dws_results = self.process_dws_layer_sequential()
                dws_duration = time.time() - dws_start_time

            # 更新任务统计
            self.task_stats['dws_daily_success'] = dws_results.get('daily_trend', False)
            self.task_stats['dws_weekly_success'] = dws_results.get('weekly_trend', False)
            self.task_stats['dws_domain_success'] = dws_results.get('domain_distribution', False)

            # 检查DWS层整体结果
            dws_success_count = sum(1 for success in dws_results.values() if success)
            dws_total_count = len(dws_results)

            logger.info(f"📈 DWS层串行处理耗时: {dws_duration:.1f}秒 ({dws_duration/60:.1f}分钟)")

            if dws_success_count == dws_total_count:
                logger.info("🎊 所有DWS层异步任务执行成功！")
            else:
                logger.warning(f"⚠️  DWS层部分异步任务失败: {dws_success_count}/{dws_total_count}")

            # 任务完成
            self.task_stats['end_time'] = datetime.datetime.now()
            self.task_stats['total_duration'] = (
                self.task_stats['end_time'] - self.task_stats['start_time']
            ).total_seconds()

            # 打印异步任务状态摘要
            logger.info("\n" + "📊 异步任务执行摘要")
            self.log_async_tasks_summary()

            # 打印执行总结
            self.print_summary()

        except Exception as e:
            logger.error(f"💥 ETL流程执行失败: {str(e)}")
            self.task_stats['end_time'] = datetime.datetime.now()
            self.task_stats['total_duration'] = (
                self.task_stats['end_time'] - self.task_stats['start_time']
            ).total_seconds() if self.task_stats['start_time'] else 0
            raise
        finally:
            # 关闭数据库连接
            logger.info("🔌 关闭数据库连接...")
            self.close_db()

    def print_summary(self):
        """打印任务执行总结"""
        logger.info("=" * 80)
        logger.info("📋 ETL任务执行总结报告")
        logger.info("=" * 80)

        # 基本信息
        logger.info("⏰ 执行时间信息:")
        logger.info(f"   开始时间: {self.task_stats['start_time']}")
        logger.info(f"   结束时间: {self.task_stats['end_time']}")
        logger.info(f"   总耗时: {self.task_stats['total_duration']:.2f} 秒")

        # 任务执行结果
        logger.info("\n📊 任务执行结果:")

        # DWD层结果
        dwd_icon = "✅" if self.task_stats['dwd_success'] else "❌"
        logger.info(f"   {dwd_icon} DWD层处理 (dwd_asset_file_details): {'成功' if self.task_stats['dwd_success'] else '失败'}")

        # DWS层结果
        dws_tasks = [
            ('DWS日度趋势 (dws_asset_storage_trend_daily)', self.task_stats['dws_daily_success']),
            ('DWS周度趋势 (dws_asset_storage_trend_weekly)', self.task_stats['dws_weekly_success']),
            ('DWS领域分布 (dws_asset_domain_distribution)', self.task_stats['dws_domain_success'])
        ]

        for task_name, success in dws_tasks:
            icon = "✅" if success else "❌"
            status = "成功" if success else "失败"
            logger.info(f"   {icon} {task_name}: {status}")

        # 统计信息
        total_tasks = 4
        success_tasks = sum([
            self.task_stats['dwd_success'],
            self.task_stats['dws_daily_success'],
            self.task_stats['dws_weekly_success'],
            self.task_stats['dws_domain_success']
        ])

        success_rate = (success_tasks / total_tasks) * 100

        logger.info("\n📈 执行统计:")
        logger.info(f"   总任务数: {total_tasks}")
        logger.info(f"   成功任务: {success_tasks}")
        logger.info(f"   失败任务: {total_tasks - success_tasks}")
        logger.info(f"   成功率: {success_rate:.1f}%")

        # 性能信息
        if self.task_stats['total_duration'] > 0:
            avg_time_per_task = self.task_stats['total_duration'] / total_tasks
            logger.info(f"   平均每任务耗时: {avg_time_per_task:.2f} 秒")

        # 最终状态
        logger.info("\n🎯 最终状态:")
        if success_rate == 100:
            logger.info("   🎉 所有ETL任务执行成功！数据仓库更新完成！")
        elif success_rate >= 75:
            logger.warning("   ⚠️  大部分ETL任务执行成功，请检查失败的任务")
            logger.warning("   建议：查看详细日志，修复失败任务后重新执行")
        else:
            logger.error("   ❌ 多个ETL任务执行失败，数据仓库更新不完整")
            logger.error("   建议：检查数据库连接、SQL语法、数据源完整性")

        # 数据质量提醒
        if success_rate == 100:
            logger.info("\n💡 数据质量提醒:")
            logger.info("   - 建议定期检查数据完整性和一致性")
            logger.info("   - 监控数据增长趋势和异常值")
            logger.info("   - 关注Superset仪表板的数据更新情况")

        logger.info("=" * 80)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Superset数据仓库ETL处理脚本 (优化版 - 支持异步任务)')
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev', help='运行环境 (dev 或 prod)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='日志级别')
    parser.add_argument('--max-retries', type=int, default=5, help='最大重试次数')
    parser.add_argument('--retry-delay', type=int, default=30, help='重试延迟时间(秒)')
    parser.add_argument('--async-mode', action='store_true', default=True, help='使用异步任务模式 (默认启用)')
    parser.add_argument('--sync-mode', action='store_true', help='强制使用同步模式 (覆盖异步模式)')
    parser.add_argument('--dws-parallel', action='store_true', help='DWS层使用并行模式 (默认串行)')
    parser.add_argument('--init-mode', action='store_true', help='初始化模式：处理全量历史数据 (默认增量模式)')
    parser.add_argument('--target-date', type=str, help='目标处理日期 (格式: YYYY-MM-DD，默认当日)')
    parser.add_argument('--incremental-strategy',
                       choices=['partition_overwrite_v2', 'native_merge', 'partition_overwrite', 'delete_insert', 'merge', 'full_overwrite'],
                       default='partition_overwrite_v2',
                       help='增量处理策略 (默认: partition_overwrite_v2，native_merge需要PRIMARY KEY表)')
    args = parser.parse_args()

    # 设置日志级别
    logging.getLogger('superset_dw_etl').setLevel(getattr(logging, args.log_level))

    # 确定执行模式
    use_async_mode = args.async_mode and not args.sync_mode
    use_dws_parallel = args.dws_parallel
    init_mode = args.init_mode
    target_date = args.target_date

    # 验证目标日期格式
    if target_date:
        try:
            datetime.datetime.strptime(target_date, '%Y-%m-%d')
        except ValueError:
            logger.error(f"❌ 目标日期格式错误: {target_date}，应为 YYYY-MM-DD 格式")
            return 1

    logger.info(f"🚀 开始执行Superset数据仓库ETL任务 (增量优化版)")
    logger.info(f"📍 运行环境: {args.env}")
    logger.info(f"📊 日志级别: {args.log_level}")
    logger.info(f"🔄 最大重试次数: {args.max_retries}")
    logger.info(f"⏱️  重试延迟: {args.retry_delay}秒")
    logger.info(f"⚡ 执行模式: {'异步任务模式' if use_async_mode else '同步模式'}")
    logger.info(f"🔀 DWS层执行: {'并行模式' if use_dws_parallel else '串行模式'}")
    logger.info(f"📋 数据处理模式: {'初始化模式 (全量历史数据)' if init_mode else '增量模式 (当日数据)'}")
    if target_date:
        logger.info(f"📅 目标处理日期: {target_date}")
    else:
        logger.info(f"📅 目标处理日期: {datetime.datetime.now().strftime('%Y-%m-%d')} (当日)")

    if use_async_mode:
        logger.info("💡 异步模式优势:")
        logger.info("   - 使用INSERT OVERWRITE替代TRUNCATE+INSERT，提高性能")
        logger.info("   - 支持长时间运行的任务，避免会话超时")
        logger.info("   - 提供详细的任务监控和状态跟踪")
        logger.info("   - 更好的错误处理和重试机制")

    if init_mode:
        logger.info("🏗️ 初始化模式特点:")
        logger.info("   - 处理全量历史数据，包含所有6个数据源")
        logger.info("   - 执行时间较长，建议在非高峰期运行")
        logger.info("   - 适用于首次建表或数据重建场景")
    else:
        logger.info("🔄 增量模式特点:")
        logger.info("   - 只处理当日新增数据，主要来源tp_crawler_record表")
        logger.info("   - 执行时间短，适合日常定时任务")
        logger.info("   - 支持重跑，会先清理目标日期数据")
        logger.info("   - 相同路径优先保留当日数据")
        logger.info(f"   - 增量策略: {args.incremental_strategy}")

        # 显示策略性能预期
        strategy_performance = {
            'partition_overwrite': '2-5分钟 (推荐)',
            'delete_insert': '5-10分钟',
            'merge': '3-8分钟',
            'full_overwrite': '2-4小时 (不推荐)'
        }
        expected_time = strategy_performance.get(args.incremental_strategy, '未知')
        logger.info(f"   - 预期执行时间: {expected_time}")

    try:
        processor = SupersetDWETLProcessor(env=args.env)
        processor.max_retries = args.max_retries
        processor.retry_delay = args.retry_delay

        # 设置执行模式标志
        processor.use_async_mode = use_async_mode
        processor.use_dws_parallel = use_dws_parallel
        processor.init_mode = init_mode
        processor.target_date = target_date

        # 设置增量处理策略
        if not init_mode:  # 只在增量模式下设置策略
            processor.set_incremental_strategy(args.incremental_strategy)

        processor.run()
        logger.info("🎉 ETL任务执行完成")
        return 0
    except Exception as e:
        logger.error(f"💥 ETL任务执行失败: {str(e)}")
        return 1
    finally:
        # 任务完成后重命名日志文件
        rename_log_file()

if __name__ == '__main__':
    main()
