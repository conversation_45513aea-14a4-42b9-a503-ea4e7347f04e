# 精简版 Superset 数据仓库 ETL 处理脚本

## 概述

这是一个基于原始 `etl_processor.py` 创建的精简版ETL处理脚本，专门处理4张核心表：

1. **dwd_asset_file_details** - 文件详情明细表 (使用异步任务)
2. **dws_asset_storage_trend_daily** - 日度存储趋势表 (使用同步任务)
3. **dws_asset_storage_trend_weekly** - 周度存储趋势表 (使用同步任务)  
4. **dws_asset_domain_distribution** - 领域分布表 (使用同步任务)

## 主要特点

### 🚀 简化的处理逻辑
- **DWD层**: 使用异步任务处理全量历史数据，不区分增量和全量模式
- **DWS层**: 使用同步 INSERT OVERWRITE，确保数据执行成功
- **错误处理**: 简化的重试机制，专注核心功能

### 📊 数据处理策略
- **dwd_asset_file_details**: 异步任务，支持长时间运行，超时时间4小时
- **DWS层表**: 同步执行，立即验证结果，确保数据一致性

### 🔍 数据验证机制
- **同步任务**: 执行成功即表示数据已更新，通过行数验证
- **异步任务**: 提交 → 等待完成 → 验证数据三步流程

## 使用方法

### 基本用法

```bash
# 开发环境执行
python simplified_etl_processor.py --env dev

# 生产环境执行  
python simplified_etl_processor.py --env prod

# 指定日志级别和重试参数
python simplified_etl_processor.py --env dev --log-level INFO --max-retries 3 --retry-delay 10
```

### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--env` | str | dev | 运行环境 (dev/prod) |
| `--log-level` | str | INFO | 日志级别 (DEBUG/INFO/WARNING/ERROR) |
| `--max-retries` | int | 3 | 最大重试次数 |
| `--retry-delay` | int | 10 | 重试延迟时间(秒) |

## 表结构和处理逻辑

### 1. dwd_asset_file_details (DWD层)

**表结构**:
```sql
CREATE TABLE `dwd_asset_file_details` (
  `hash_key` varchar(40) NOT NULL COMMENT "路径哈希Key(MURMUR_HASH3_32(path))",
  `path` varchar(65533) NOT NULL COMMENT "文件唯一路径",
  `file_size` bigint(20) NULL COMMENT "文件大小 (Bytes)",
  `cre_dt` datetime NULL COMMENT "文件创建时间",
  `domain` varchar(255) NULL COMMENT "所属领域",
  `dataset_name` varchar(255) NULL COMMENT "所属数据集名称",
  `source_table` varchar(50) NULL COMMENT "数据来源表"
) ENGINE=OLAP PRIMARY KEY(`hash_key`)
```

**处理逻辑**:
- 整合6个数据源表的全量历史数据
- 按path去重，优先保留source_priority高的记录
- 使用异步任务，支持长时间运行
- 生成hash_key作为主键

### 2. dws_asset_storage_trend_daily (DWS层)

**表结构**:
```sql
CREATE TABLE `dws_asset_storage_trend_daily` (
  `stat_date` date NOT NULL COMMENT "统计日期",
  `daily_storage_tb` decimal(18, 6) NULL COMMENT "当日新增存储量 (TB)",
  `cumulative_storage_tb` decimal(18, 6) NULL COMMENT "截止当日累计存储量 (TB)",
  `daily_dod_pct` decimal(18, 2) NULL COMMENT "日存储量日环比 (%)",
  `cumulative_dod_pct` decimal(18, 2) NULL COMMENT "累计存储量日环比 (%)"
) ENGINE=OLAP DUPLICATE KEY(`stat_date`)
```

**处理逻辑**:
- 基于dwd_asset_file_details按天聚合
- 计算每日新增量和累计量
- 计算日环比百分比

### 3. dws_asset_storage_trend_weekly (DWS层)

**表结构**:
```sql
CREATE TABLE `dws_asset_storage_trend_weekly` (
  `week_start_date` date NOT NULL COMMENT "统计周的周一日期",
  `weekly_storage_tb` decimal(18, 6) NULL COMMENT "当周新增存储量 (TB)",
  `cumulative_storage_tb` decimal(18, 6) NULL COMMENT "截止当周累计存储量 (TB)",
  `weekly_wow_pct` decimal(18, 2) NULL COMMENT "周存储量周环比 (%)",
  `cumulative_wow_pct` decimal(18, 2) NULL COMMENT "累计存储量周环比 (%)"
) ENGINE=OLAP DUPLICATE KEY(`week_start_date`)
```

**处理逻辑**:
- 基于dwd_asset_file_details按周聚合
- 使用DATE_TRUNC('week', cre_dt)获取周一日期
- 计算每周新增量和累计量
- 计算周环比百分比

### 4. dws_asset_domain_distribution (DWS层)

**表结构**:
```sql
CREATE TABLE `dws_asset_domain_distribution` (
  `domain` varchar(255) NOT NULL COMMENT "所属领域",
  `dataset_count` bigint(20) NULL COMMENT "该领域下数据集总数",
  `size_in_tb` decimal(18, 6) NULL COMMENT "该领域下数据总大小 (TB)"
) ENGINE=OLAP DUPLICATE KEY(`domain`)
```

**处理逻辑**:
- 基于dwd_asset_file_details按domain聚合
- 统计每个领域的数据集数量和总大小
- 过滤domain为NULL的记录

## 执行流程

### 第一阶段：DWD层处理 (异步任务)
1. 提交异步任务到StarRocks
2. 监控任务执行状态 (最长等待5小时)
3. 验证数据完整性

### 第二阶段：DWS层处理 (同步任务)
1. 串行执行3个DWS表的处理
2. 每个表使用INSERT OVERWRITE
3. 立即验证执行结果

## 监控和日志

### 日志文件
- 位置: `logs/simplified_etl_processor.log`
- 格式: 时间戳 - 日志级别 - 消息内容

### 关键监控指标
- **任务执行时间**: DWD层预计10分钟内，DWS层每个表1-2分钟
- **数据行数**: 验证各表数据是否正确写入
- **成功率**: 4个任务的整体成功率

## 故障排查

### 常见问题

1. **DWD异步任务超时**
   - 检查集群负载情况
   - 考虑在非高峰期执行
   - 查看task_runs表的错误信息

2. **DWS同步任务失败**
   - 检查DWD表数据是否存在
   - 验证SQL语法是否正确
   - 查看详细错误日志

3. **数据验证失败**
   - 检查表结构是否匹配
   - 验证数据源表是否有数据
   - 确认权限配置正确

### 调试命令

```bash
# 查看异步任务状态
SELECT * FROM information_schema.task_runs WHERE TASK_NAME LIKE 'dwd_asset_file_details_%' ORDER BY CREATE_TIME DESC LIMIT 5;

# 验证表数据
SELECT COUNT(*) FROM dwd_asset_file_details;
SELECT COUNT(*) FROM dws_asset_storage_trend_daily;
SELECT COUNT(*) FROM dws_asset_storage_trend_weekly;  
SELECT COUNT(*) FROM dws_asset_domain_distribution;
```

## 与原版本的差异

| 特性 | 原版本 | 精简版本 |
|------|--------|----------|
| 处理模式 | 支持增量/全量 | 仅全量模式 |
| DWD策略 | 多种增量策略 | 固定异步任务 |
| DWS处理 | 异步/同步可选 | 固定同步处理 |
| 错误处理 | 复杂重试机制 | 简化重试机制 |
| 配置选项 | 20+参数 | 4个核心参数 |
| 代码行数 | 1900+ | 800- |

## 性能预期

- **DWD层**: 10分钟内完成 (取决于数据量)
- **DWS日度趋势**: 1-2分钟
- **DWS周度趋势**: 1-2分钟  
- **DWS领域分布**: 30秒-1分钟
- **总执行时间**: 15分钟内

## 注意事项

1. **数据依赖**: DWS层依赖DWD层数据，必须先完成DWD层处理
2. **异步任务**: DWD层使用异步任务，需要等待任务完成才能继续
3. **数据一致性**: DWS层使用INSERT OVERWRITE确保数据一致性
4. **资源使用**: 异步任务会占用集群资源，建议在非高峰期执行
