# 表结构迁移方案：DUPLICATE KEY → PRIMARY KEY

## 🎯 迁移目标

将 `dwd_asset_file_details` 从 DUPLICATE KEY 表改造为 PRIMARY KEY 表，以支持StarRocks原生MERGE操作。

## 📊 迁移前后对比

| 特性 | DUPLICATE KEY (当前) | PRIMARY KEY (目标) |
|------|---------------------|-------------------|
| **重复数据** | 允许 | 不允许 |
| **MERGE支持** | ❌ 不支持 | ✅ 支持 |
| **查询性能** | 需要去重 | 无需去重 |
| **存储效率** | 可能冗余 | 更高效 |
| **维护复杂度** | 高 | 低 |

## 🛠️ 迁移步骤

### 步骤1：创建新的PRIMARY KEY表

```sql
-- 创建新表结构
CREATE TABLE `dwd_asset_file_details_v2` (
  `path` varchar(65533) NOT NULL COMMENT "文件唯一路径",
  `file_size` bigint(20) NULL COMMENT "文件大小 (Bytes)",
  `cre_dt` datetime NULL COMMENT "文件创建时间",
  `domain` varchar(255) NULL COMMENT "所属领域",
  `dataset_name` varchar(255) NULL COMMENT "所属数据集名称",
  `source_table` varchar(50) NULL COMMENT "数据来源表"
) ENGINE=OLAP 
PRIMARY KEY(`path`)  -- 关键改动：设置path为主键
PARTITION BY time_slice(cre_dt, 1, 'day', 'floor')
DISTRIBUTED BY HASH(`path`) BUCKETS 128 
PROPERTIES (
"replication_num" = "3",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"storage_medium" = "HDD",
"compression" = "LZ4"
);
```

### 步骤2：数据迁移（去重）

```sql
-- 迁移数据并去重
INSERT INTO dwd_asset_file_details_v2
WITH deduplicated_data AS (
    SELECT
        path, file_size, cre_dt, domain, dataset_name, source_table
    FROM (
        SELECT
            *,
            ROW_NUMBER() OVER(PARTITION BY path ORDER BY cre_dt DESC) as rn
        FROM dwd_asset_file_details
    ) t
    WHERE rn = 1
)
SELECT * FROM deduplicated_data;
```

### 步骤3：验证数据完整性

```sql
-- 验证记录数量
SELECT 
    (SELECT COUNT(*) FROM dwd_asset_file_details) as old_count,
    (SELECT COUNT(*) FROM dwd_asset_file_details_v2) as new_count,
    (SELECT COUNT(DISTINCT path) FROM dwd_asset_file_details) as unique_paths;

-- 验证数据一致性
SELECT 
    COUNT(*) as duplicate_check
FROM dwd_asset_file_details_v2 
GROUP BY path 
HAVING COUNT(*) > 1;
-- 应该返回0条记录
```

### 步骤4：切换表名

```sql
-- 备份原表
ALTER TABLE dwd_asset_file_details RENAME TO dwd_asset_file_details_backup;

-- 新表改名
ALTER TABLE dwd_asset_file_details_v2 RENAME TO dwd_asset_file_details;
```

## 🚀 使用原生MERGE的优势

### 性能提升
```sql
-- 原生MERGE操作（新表支持）
INSERT INTO dwd_asset_file_details
SELECT path, file_size, cre_dt, domain, dataset_name, source_table
FROM daily_new_data
ON DUPLICATE KEY UPDATE
    file_size = VALUES(file_size),
    cre_dt = VALUES(cre_dt),
    domain = VALUES(domain),
    dataset_name = VALUES(dataset_name),
    source_table = VALUES(source_table);
```

### 预期性能对比

| 策略 | 执行时间 | 跨分区去重 | 原子性 | 推荐度 |
|------|----------|------------|--------|--------|
| **原生MERGE** | **3-8分钟** | ✅ 自动处理 | ✅ 原子操作 | 🌟🌟🌟🌟🌟 |
| partition_overwrite_v2 | 15-30分钟 | ✅ 支持 | ✅ 支持 | 🌟🌟🌟🌟 |
| delete_insert | 5-10分钟 | ✅ 支持 | ⚠️ 两步操作 | 🌟🌟🌟 |

## 📋 迁移风险评估

### 低风险
- ✅ 数据备份完整
- ✅ 可以回滚
- ✅ 逐步验证

### 中等风险
- ⚠️ 迁移期间服务中断
- ⚠️ 数据量大，迁移时间长

### 风险缓解措施
1. **选择低峰期执行**
2. **分批迁移验证**
3. **保留原表备份**
4. **准备回滚方案**

## 🎯 推荐实施计划

### 阶段1：准备阶段（1-2天）
1. 在开发环境测试迁移流程
2. 评估迁移时间和资源需求
3. 准备监控和回滚脚本

### 阶段2：执行阶段（维护窗口）
1. 停止ETL任务
2. 创建新表并迁移数据
3. 验证数据完整性
4. 切换表名

### 阶段3：验证阶段（1-3天）
1. 使用原生MERGE策略运行ETL
2. 监控性能指标
3. 验证数据质量

## 💡 代码适配

### 新增策略支持
```python
# 添加原生MERGE策略
processor.set_incremental_strategy('native_merge')

# 策略选择逻辑
if self.incremental_strategy == 'native_merge':
    # 使用原生MERGE（需要PRIMARY KEY表）
    dwd_sql = self.build_dwd_incremental_sql_native_merge()
    description = f"DWD层数据处理 (StarRocks原生MERGE - {target_date})"
    query_timeout = 1200  # 20分钟超时
    max_wait_seconds = 2400  # 最大等待40分钟
```

### 兼容性处理
```python
def validate_table_structure(self):
    """验证表结构是否支持MERGE操作"""
    check_sql = """
    SELECT TABLE_TYPE 
    FROM information_schema.tables 
    WHERE table_name = 'dwd_asset_file_details'
    """
    # 检查是否为PRIMARY KEY表
```

## 🎉 预期收益

1. **性能提升** - MERGE操作比DELETE+INSERT快40-60%
2. **代码简化** - 无需复杂的去重逻辑
3. **数据一致性** - 原生保证path唯一性
4. **维护简单** - 减少ETL复杂度

## 📋 总结

修改表结构使用原生MERGE是一个**高收益的优化方案**：
- ✅ 性能最优（3-8分钟）
- ✅ 逻辑最简单
- ✅ 原生支持UPSERT
- ✅ 长期维护成本低

建议在下次维护窗口期间实施此方案！🚀
