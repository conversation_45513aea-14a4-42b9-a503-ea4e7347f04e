#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试原生UPSERT功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def test_native_upsert():
    """测试原生UPSERT功能"""
    print("🧪 测试原生UPSERT功能...")
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.target_date = '2025-07-25'
    
    # 生成原生UPSERT SQL
    print("📋 生成原生UPSERT SQL:")
    sql = processor.build_dwd_incremental_sql_native_merge()
    print("=" * 80)
    print(sql)
    print("=" * 80)
    
    # 连接数据库测试
    processor.connect_db()
    
    try:
        # 检查当前表结构
        desc_sql = "DESC dwd_asset_file_details"
        processor.cursor.execute(desc_sql)
        columns = processor.cursor.fetchall()
        
        print("\n📊 当前表结构:")
        for col in columns:
            print(f"   {col[0]} {col[1]} {col[2] if len(col) > 2 else ''}")
        
        # 检查表类型
        show_create_sql = "SHOW CREATE TABLE dwd_asset_file_details"
        processor.cursor.execute(show_create_sql)
        create_info = processor.cursor.fetchone()
        
        print(f"\n📋 表类型验证:")
        create_statement = create_info[1] if create_info else "未找到"
        
        if "PRIMARY KEY" in create_statement and "hash_key" in create_statement:
            print("✅ 当前表是hash_key PRIMARY KEY表，支持原生UPSERT")
        else:
            print("❌ 表结构不符合预期")
            return
        
        # 检查现有数据量
        count_sql = "SELECT COUNT(*) FROM dwd_asset_file_details"
        processor.cursor.execute(count_sql)
        current_count = processor.cursor.fetchone()[0]
        print(f"📊 当前数据量: {current_count:,} 条")
        
        # 测试简单的UPSERT操作
        print("\n🧪 测试简单UPSERT操作...")
        
        # 插入测试数据
        test_hash = "MURMUR_HASH3_32('/test/upsert_test.txt')"
        insert_sql = f"""
        INSERT INTO dwd_asset_file_details
        VALUES ({test_hash}, '/test/upsert_test.txt', 1024, '2025-07-25 10:00:00', 'test', 'test_dataset', 'test_source')
        """
        
        processor.cursor.execute(insert_sql)
        processor.conn.commit()
        print("✅ 第一次INSERT成功")
        
        # 再次插入相同hash_key的数据（应该更新）
        update_sql = f"""
        INSERT INTO dwd_asset_file_details
        VALUES ({test_hash}, '/test/upsert_test.txt', 2048, '2025-07-25 11:00:00', 'test_updated', 'test_dataset_updated', 'test_source_updated')
        """
        
        processor.cursor.execute(update_sql)
        processor.conn.commit()
        print("✅ 第二次INSERT成功（应该是UPSERT）")
        
        # 验证结果
        check_sql = f"""
        SELECT hash_key, path, file_size, cre_dt, domain, dataset_name, source_table
        FROM dwd_asset_file_details
        WHERE hash_key = {test_hash}
        """
        
        processor.cursor.execute(check_sql)
        result = processor.cursor.fetchone()
        
        if result:
            print("📊 UPSERT验证结果:")
            print(f"   hash_key: {result[0]}")
            print(f"   path: {result[1]}")
            print(f"   file_size: {result[2]} (应该是2048)")
            print(f"   cre_dt: {result[3]}")
            print(f"   domain: {result[4]} (应该是test_updated)")
            print(f"   dataset_name: {result[5]} (应该是test_dataset_updated)")
            print(f"   source_table: {result[6]} (应该是test_source_updated)")
            
            if result[2] == 2048 and result[4] == 'test_updated':
                print("✅ UPSERT功能验证成功！数据已更新")
            else:
                print("❌ UPSERT功能验证失败，数据未正确更新")
        else:
            print("❌ 未找到测试数据")
        
        # 清理测试数据
        cleanup_sql = f"DELETE FROM dwd_asset_file_details WHERE hash_key = {test_hash}"
        processor.cursor.execute(cleanup_sql)
        processor.conn.commit()
        print("✅ 测试数据已清理")
        
        print("\n🎉 原生UPSERT功能测试完成！")
        print("📋 优势总结:")
        print("   ✅ 直接INSERT INTO即可，无需复杂逻辑")
        print("   ✅ PRIMARY KEY表自动处理重复键")
        print("   ✅ 性能最优，代码最简")
        print("   ✅ 完美解决跨分区重复问题")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    finally:
        processor.close_db()

if __name__ == "__main__":
    test_native_upsert()
