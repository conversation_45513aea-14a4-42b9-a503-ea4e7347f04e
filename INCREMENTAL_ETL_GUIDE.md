# ETL增量处理使用指南

## 🎯 增量优化方案

基于您的需求，我们实现了一个高效的增量ETL处理方案，解决了DWD层处理时间过长的问题。

## 📊 方案特点

### 🏗️ 双模式设计

| 模式 | 用途 | 数据范围 | 执行频率 | 性能特点 |
|------|------|----------|----------|----------|
| **初始化模式** | 首次建表/重建 | 全量历史数据 | 一次性 | 时间长，数据全 |
| **增量模式** | 日常更新 | 当日新增数据 | 每日执行 | 时间短，效率高 |

### 🔄 数据源优化

- **活跃数据源**: `tp_crawler_record` (持续更新)
- **历史数据源**: 其他5个表 (已停止维护)
- **智能合并**: 相同路径优先保留当日数据

## 🚀 使用方法

### 1. 初始化模式 (一次性执行)

```bash
# 处理全量历史数据
./run_etl_async.sh --env prod --init-mode

# 或直接调用Python脚本
python3 etl_processor.py --env prod --init-mode
```

**特点**:
- 处理所有6个数据源的历史数据
- 执行时间较长 (预计2-4小时)
- 建议在非高峰期执行
- 适用于首次建表或数据重建

### 2. 增量模式 (日常执行)

```bash
# 处理当日数据 (默认模式)
./run_etl_async.sh --env prod

# 处理指定日期数据
./run_etl_async.sh --env prod --target-date 2024-12-17

# 或直接调用Python脚本
python3 etl_processor.py --env prod --target-date 2024-12-17
```

**特点**:
- 只处理当日新增数据
- 执行时间短 (预计10-30分钟)
- 支持重跑，自动清理目标日期数据
- 适合定时任务

## 🔧 技术实现

### 增量处理逻辑

```sql
-- 1. 获取历史数据 (排除目标日期)
historical_data AS (
    SELECT path, file_size, cre_dt, domain, dataset_name, source_table
    FROM dwd_asset_file_details
    WHERE DATE(cre_dt) != '2024-12-18'
),

-- 2. 获取当日新增数据 (主要来源：tp_crawler_record)
daily_new_data AS (
    SELECT path, file_size, cre_dt, domain, dataset_name, source_table
    FROM tp_crawler_record
    WHERE DATE(cre_dt) = '2024-12-18'
),

-- 3. 智能合并：相同路径优先保留当日数据
deduplicated_final AS (
    SELECT *
    FROM (
        SELECT *, ROW_NUMBER() OVER(
            PARTITION BY path 
            ORDER BY source_priority ASC, cre_dt DESC
        ) as rn
        FROM all_data_combined
    ) t
    WHERE rn = 1
)
```

### 重跑机制

增量模式支持重跑，会自动：
1. 清理目标日期的现有数据
2. 重新处理当日新增数据
3. 与历史数据智能合并

## 📈 性能对比

| 指标 | 全量模式 | 增量模式 | 改进 |
|------|----------|----------|------|
| 执行时间 | 2-4小时 | 10-30分钟 | 🚀 80%+ 提升 |
| 数据扫描量 | 全量历史 | 当日增量 | 🔍 大幅减少 |
| 资源消耗 | 高 | 低 | 💡 显著优化 |
| 适用场景 | 初始化 | 日常更新 | ✅ 场景明确 |

## 🎯 DWS层处理策略

**建议**: DWS层继续使用 `INSERT OVERWRITE` 全量重建

**原因**:
- DWS是基于DWD的聚合计算
- 日度/周度趋势需要完整时间序列
- 领域分布需要全量统计
- 数据量相对较小，性能影响有限

## 📅 定时任务配置

### Cron配置示例

```bash
# 每日凌晨2点执行增量ETL
0 2 * * * /path/to/run_etl_async.sh --env prod

# 每周日凌晨1点执行初始化模式 (可选，用于数据校验)
0 1 * * 0 /path/to/run_etl_async.sh --env prod --init-mode
```

### Kubernetes CronJob

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: superset-etl-incremental
spec:
  schedule: "0 2 * * *"  # 每日凌晨2点
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: etl-processor
            image: your-registry/superset-etl:latest
            command: ["python3", "etl_processor.py"]
            args: ["--env", "prod"]  # 默认增量模式
```

## 🔍 监控和验证

### 数据验证查询

```sql
-- 检查当日数据处理情况
SELECT 
    DATE(cre_dt) as process_date,
    COUNT(*) as record_count,
    COUNT(DISTINCT source_table) as source_count,
    SUM(file_size) / POWER(1024, 4) as total_size_tb
FROM dwd_asset_file_details
WHERE DATE(cre_dt) = CURDATE()
GROUP BY DATE(cre_dt);

-- 检查数据源分布
SELECT 
    source_table,
    COUNT(*) as record_count,
    MIN(cre_dt) as earliest_date,
    MAX(cre_dt) as latest_date
FROM dwd_asset_file_details
GROUP BY source_table
ORDER BY latest_date DESC;
```

### 任务监控

```sql
-- 查看最近的异步任务执行情况
SELECT 
    TASK_NAME,
    STATE,
    CREATE_TIME,
    FINISH_TIME,
    PROGRESS
FROM information_schema.task_runs
WHERE TASK_NAME LIKE 'dwd_asset_file_details_%'
ORDER BY CREATE_TIME DESC
LIMIT 10;
```

## ⚠️ 注意事项

### 数据一致性
- 增量模式依赖DWD表的历史数据完整性
- 首次使用前必须执行初始化模式
- 建议定期执行数据校验

### 性能优化
- 增量模式主要处理tp_crawler_record表
- 其他表数据作为历史基准，不再更新
- 相同路径的数据优先保留最新日期

### 故障恢复
- 支持任意日期的重跑
- 重跑会自动清理目标日期数据
- 不会影响其他日期的数据

## 🎉 使用建议

1. **首次部署**: 使用初始化模式建立基准数据
2. **日常运行**: 使用增量模式处理当日数据
3. **数据修复**: 使用target-date参数重跑特定日期
4. **性能监控**: 关注任务执行时间和数据质量
5. **定期校验**: 可选择性执行初始化模式进行数据校验

这个增量方案将大幅提升ETL性能，特别适合您当前只有tp_crawler_record表在持续更新的场景！
