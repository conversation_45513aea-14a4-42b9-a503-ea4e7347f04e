# Superset数据仓库ETL任务Docker镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY etl_processor.py .

# 创建日志目录
RUN mkdir -p /app/logs/superset_dw_etl

# 设置权限
RUN chmod +x etl_processor.py

# 默认命令
CMD ["python", "etl_processor.py", "--env", "prod", "--log-level", "INFO"]
