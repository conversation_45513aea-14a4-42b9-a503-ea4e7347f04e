# Superset数据仓库ETL异步优化方案

## 🚀 优化概述

本次优化将原有的 `TRUNCATE TABLE + INSERT INTO` 模式升级为基于StarRocks `SUBMIT TASK` 的异步 `INSERT OVERWRITE` 模式，显著提升了ETL任务的性能、稳定性和可监控性。

## ✨ 主要改进

### 1. 异步任务架构
- **替换方案**: `INSERT OVERWRITE` 替代 `TRUNCATE + INSERT`
- **异步执行**: 使用 `SUBMIT TASK` 提交长时间运行的任务
- **任务监控**: 通过 `information_schema.task_runs` 实时监控任务状态
- **超时控制**: 支持自定义查询超时和等待时间

### 2. 性能优化
- **原子操作**: `INSERT OVERWRITE` 确保数据一致性
- **避免锁竞争**: 减少表锁定时间
- **会话独立**: 任务提交后不依赖客户端会话
- **资源管理**: 更好的集群资源利用

### 3. 监控增强
- **实时状态**: 详细的任务执行状态跟踪
- **进度显示**: 支持任务执行进度监控
- **错误诊断**: 完整的错误信息和调试支持
- **历史记录**: 任务执行历史和统计信息

### 4. 稳定性提升
- **重试机制**: 智能重试策略
- **错误处理**: 完善的异常处理和恢复
- **资源控制**: 避免异步任务间的资源竞争
- **串行执行**: DWS层推荐使用串行模式确保稳定性

## 📁 文件结构

```
superset_dw_job/
├── etl_processor.py          # 优化后的主ETL脚本
├── run_etl_async.sh         # 便捷执行脚本
├── README_ASYNC_OPTIMIZATION.md  # 本文档
└── logs/                    # 日志目录
    ├── etl_processor.log    # 当前日志
    └── etl_processor-YYYYMMDD.log  # 历史日志
```

## 🔧 使用方法

### 基本用法

```bash
# 使用默认配置 (开发环境，异步模式)
./run_etl_async.sh

# 生产环境执行
./run_etl_async.sh --env prod

# 查看帮助信息
./run_etl_async.sh --help
```

### 高级配置

```bash
# 生产环境，并行DWS处理，增加重试次数
./run_etl_async.sh --env prod --parallel --max-retries 10

# 开发环境，同步模式，调试日志
./run_etl_async.sh --env dev --sync-mode --log-level DEBUG

# 自定义超时和重试设置
./run_etl_async.sh --env prod --retry-delay 60 --max-retries 8
```

### 直接调用Python脚本

```bash
# 异步模式 (推荐)
python3 etl_processor.py --env prod --async-mode

# 同步模式 (兼容性)
python3 etl_processor.py --env dev --sync-mode

# DWS层并行处理
python3 etl_processor.py --env prod --dws-parallel
```

## 📊 执行流程

### 异步模式流程

1. **DWD层处理**
   ```sql
   SUBMIT /*+set_var(query_timeout=7200)*/ TASK dwd_asset_file_details_20241218_143022_123 AS
   INSERT OVERWRITE dwd_asset_file_details
   WITH all_sources_unioned AS (...)
   SELECT ...
   ```

2. **任务状态监控**
   ```sql
   SELECT task_name, state, progress, error_message
   FROM information_schema.task_runs
   WHERE task_name = 'dwd_asset_file_details_20241218_143022_123'
   ```

3. **DWS层串行处理**
   - 日度趋势表 → 周度趋势表 → 领域分布表
   - 每个任务独立提交和监控
   - 前一个任务成功后才执行下一个

### 同步模式流程 (兼容性)

1. **传统TRUNCATE + INSERT模式**
2. **直接SQL执行和提交**
3. **简单的重试机制**

## 🔍 监控和调试

### 任务状态查询

```sql
-- 查看所有异步任务状态
SELECT * FROM information_schema.task_runs 
ORDER BY create_time DESC;

-- 查看特定任务详情
SELECT * FROM information_schema.task_runs 
WHERE task_name LIKE 'dwd_asset_file_details_%'
ORDER BY create_time DESC;

-- 查看失败任务
SELECT task_name, error_message, create_time, finish_time
FROM information_schema.task_runs 
WHERE state = 'FAILED'
ORDER BY create_time DESC;
```

### 日志分析

```bash
# 查看最新日志
tail -f logs/etl_processor.log

# 搜索错误信息
grep -i "error\|failed\|exception" logs/etl_processor.log

# 查看任务状态变化
grep -i "任务.*状态" logs/etl_processor.log
```

## ⚙️ 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--env` | dev | 运行环境 (dev/prod) |
| `--async-mode` | true | 使用异步任务模式 |
| `--sync-mode` | false | 强制使用同步模式 |
| `--dws-parallel` | false | DWS层并行处理 |
| `--max-retries` | 5 | 最大重试次数 |
| `--retry-delay` | 30 | 重试延迟(秒) |
| `--log-level` | INFO | 日志级别 |

## 🎯 性能对比

| 指标 | 原始模式 | 异步优化模式 | 改进 |
|------|----------|--------------|------|
| DWD层执行 | TRUNCATE + INSERT | INSERT OVERWRITE | 原子性 ✅ |
| 会话依赖 | 强依赖 | 无依赖 | 稳定性 ✅ |
| 超时处理 | 会话超时 | 任务级超时 | 可靠性 ✅ |
| 监控能力 | 基础日志 | 详细状态跟踪 | 可观测性 ✅ |
| 错误恢复 | 手动重试 | 智能重试 | 自动化 ✅ |
| 资源利用 | 同步阻塞 | 异步非阻塞 | 效率 ✅ |

## 🚨 注意事项

### 异步模式注意点

1. **任务命名**: 自动生成唯一任务名，避免冲突
2. **超时设置**: 根据数据量调整查询超时时间
3. **资源竞争**: DWS层推荐串行模式避免资源竞争
4. **监控频率**: 15秒检查间隔，平衡性能和实时性

### 故障排除

1. **任务提交失败**
   - 检查数据库连接
   - 验证SQL语法
   - 查看集群资源状态

2. **任务执行超时**
   - 增加 `query_timeout` 设置
   - 检查数据量和集群负载
   - 考虑分批处理

3. **任务状态异常**
   - 查询 `information_schema.task_runs`
   - 检查错误信息和错误代码
   - 分析SQL执行计划

## 🔮 未来扩展

1. **分区优化**: 支持分区表的增量更新
2. **并行优化**: 智能的DWS层并行执行策略
3. **监控集成**: 集成Prometheus/Grafana监控
4. **自动调优**: 基于历史数据的参数自动调优
5. **故障恢复**: 自动故障检测和恢复机制

## 📞 支持

如有问题或建议，请查看日志文件或联系开发团队。

---

**版本**: 2.0 (异步优化版)  
**更新时间**: 2024-12-18  
**兼容性**: StarRocks 2.5+
