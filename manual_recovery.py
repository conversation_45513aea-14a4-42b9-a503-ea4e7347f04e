#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
手动恢复脚本 - 恢复备份表
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def manual_recovery():
    """手动恢复备份表"""
    print("🔧 手动恢复备份表...")
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.connect_db()
    
    try:
        # 查看当前表状态
        show_tables_sql = "SHOW TABLES"
        processor.cursor.execute(show_tables_sql)
        all_tables = processor.cursor.fetchall()

        # 过滤出相关表
        tables = [table for table in all_tables if 'dwd_asset_file_details' in table[0]]
        
        print("📋 当前表状态:")
        if tables:
            for table in tables:
                print(f"   - {table[0]}")
        else:
            print("   - 未找到相关表")

        # 查找备份表
        backup_tables = [table[0] for table in tables if 'backup' in table[0]]
        
        if backup_tables:
            latest_backup = sorted(backup_tables)[-1]  # 获取最新的备份表
            print(f"🔍 找到备份表: {latest_backup}")
            
            # 检查是否存在主表
            main_table_exists = any('dwd_asset_file_details' == table[0] for table in tables)
            
            if main_table_exists:
                print("⚠️ 主表仍然存在，先删除...")
                drop_sql = "DROP TABLE IF EXISTS dwd_asset_file_details"
                processor.cursor.execute(drop_sql)
                processor.conn.commit()
                print("✅ 主表已删除")
            
            # 恢复备份表
            restore_sql = f"ALTER TABLE {latest_backup} RENAME TO dwd_asset_file_details"
            processor.cursor.execute(restore_sql)
            processor.conn.commit()
            print(f"✅ 已从 {latest_backup} 恢复主表")
            
        else:
            print("❌ 未找到备份表")
            
    except Exception as e:
        print(f"❌ 恢复失败: {str(e)}")
    
    finally:
        processor.close_db()

if __name__ == "__main__":
    manual_recovery()
