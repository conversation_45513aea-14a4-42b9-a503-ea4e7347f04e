#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
StarRocks增量处理策略测试脚本

用于测试和验证不同增量处理策略的SQL生成和性能表现
"""

import sys
import os
import datetime
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def test_sql_generation():
    """测试SQL生成功能"""
    print("=" * 80)
    print("🧪 测试SQL生成功能")
    print("=" * 80)
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.target_date = '2024-01-01'
    
    strategies = [
        ('partition_overwrite', '分区级别INSERT OVERWRITE'),
        ('delete_insert', 'DELETE+INSERT'),
        ('merge', 'MERGE操作'),
        ('full_overwrite', '全表INSERT OVERWRITE')
    ]
    
    for strategy, description in strategies:
        print(f"\n📋 策略: {strategy} ({description})")
        print("-" * 60)
        
        try:
            if strategy == 'partition_overwrite':
                sql = processor.build_dwd_incremental_sql()
            elif strategy == 'delete_insert':
                sql = processor.build_dwd_incremental_sql_delete_insert()
            elif strategy == 'merge':
                sql = processor.build_dwd_incremental_sql_merge()
            elif strategy == 'full_overwrite':
                sql = processor.build_dwd_incremental_sql_full_overwrite()
            
            # 显示SQL的前200个字符
            sql_preview = sql.strip()[:200] + "..." if len(sql.strip()) > 200 else sql.strip()
            print(f"✅ SQL生成成功")
            print(f"📝 SQL预览: {sql_preview}")
            print(f"📏 SQL长度: {len(sql)} 字符")
            
        except Exception as e:
            print(f"❌ SQL生成失败: {str(e)}")

def test_strategy_switching():
    """测试策略切换功能"""
    print("\n" + "=" * 80)
    print("🔄 测试策略切换功能")
    print("=" * 80)
    
    processor = SupersetDWETLProcessor(env='dev')
    
    strategies = ['partition_overwrite', 'delete_insert', 'merge', 'full_overwrite']
    
    for strategy in strategies:
        try:
            processor.set_incremental_strategy(strategy)
            print(f"✅ 成功切换到策略: {strategy}")
        except Exception as e:
            print(f"❌ 切换策略失败: {strategy} - {str(e)}")
    
    # 测试无效策略
    try:
        processor.set_incremental_strategy('invalid_strategy')
        print("❌ 应该拒绝无效策略")
    except ValueError:
        print("✅ 正确拒绝了无效策略")

def analyze_sql_complexity():
    """分析不同策略的SQL复杂度"""
    print("\n" + "=" * 80)
    print("📊 分析SQL复杂度")
    print("=" * 80)
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.target_date = '2024-01-01'
    
    strategies = {
        'partition_overwrite': processor.build_dwd_incremental_sql,
        'delete_insert': processor.build_dwd_incremental_sql_delete_insert,
        'merge': processor.build_dwd_incremental_sql_merge,
        'full_overwrite': processor.build_dwd_incremental_sql_full_overwrite
    }
    
    print(f"{'策略':<20} {'SQL长度':<10} {'CTE数量':<10} {'复杂度':<10}")
    print("-" * 60)
    
    for strategy, sql_func in strategies.items():
        try:
            sql = sql_func()
            sql_length = len(sql)
            cte_count = sql.upper().count('WITH') + sql.upper().count('AS (')
            
            # 简单的复杂度评估
            if sql_length < 1000:
                complexity = "低"
            elif sql_length < 2000:
                complexity = "中"
            else:
                complexity = "高"
            
            print(f"{strategy:<20} {sql_length:<10} {cte_count:<10} {complexity:<10}")
            
        except Exception as e:
            print(f"{strategy:<20} {'错误':<10} {'N/A':<10} {'N/A':<10}")

def performance_estimation():
    """性能预估"""
    print("\n" + "=" * 80)
    print("⚡ 性能预估 (基于18亿+数据量)")
    print("=" * 80)
    
    performance_data = {
        'partition_overwrite': {
            '扫描数据量': '当日数据 (~100万条)',
            '预估时间': '2-5分钟',
            '资源消耗': '低',
            '推荐度': '🌟🌟🌟🌟🌟'
        },
        'delete_insert': {
            '扫描数据量': '当日数据 (~100万条)',
            '预估时间': '5-10分钟',
            '资源消耗': '中',
            '推荐度': '🌟🌟🌟🌟'
        },
        'merge': {
            '扫描数据量': '当日数据 (~100万条)',
            '预估时间': '3-8分钟',
            '资源消耗': '中',
            '推荐度': '🌟🌟🌟🌟'
        },
        'full_overwrite': {
            '扫描数据量': '全表数据 (18亿+条)',
            '预估时间': '2-4小时',
            '资源消耗': '极高',
            '推荐度': '🌟'
        }
    }
    
    for strategy, metrics in performance_data.items():
        print(f"\n📋 {strategy}:")
        for metric, value in metrics.items():
            print(f"   {metric}: {value}")

def generate_usage_examples():
    """生成使用示例"""
    print("\n" + "=" * 80)
    print("📚 使用示例")
    print("=" * 80)
    
    examples = [
        {
            'scenario': '日常增量处理 (推荐)',
            'command': 'python etl_processor.py --env prod --incremental-strategy partition_overwrite',
            'description': '使用分区级别INSERT OVERWRITE，性能最优'
        },
        {
            'scenario': '数据修复场景',
            'command': 'python etl_processor.py --env prod --incremental-strategy delete_insert --target-date 2024-01-01',
            'description': '先删除再插入，逻辑清晰'
        },
        {
            'scenario': '频繁更新场景',
            'command': 'python etl_processor.py --env prod --incremental-strategy merge',
            'description': '使用MERGE操作，支持UPSERT语义'
        },
        {
            'scenario': '紧急备选方案',
            'command': 'python etl_processor.py --env prod --incremental-strategy full_overwrite',
            'description': '全表重写，性能较差但逻辑简单'
        }
    ]
    
    for example in examples:
        print(f"\n🎯 {example['scenario']}:")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['description']}")

def main():
    """主函数"""
    print("🚀 StarRocks增量处理策略测试")
    
    # 运行所有测试
    test_sql_generation()
    test_strategy_switching()
    analyze_sql_complexity()
    performance_estimation()
    generate_usage_examples()
    
    print("\n" + "=" * 80)
    print("✅ 所有测试完成")
    print("=" * 80)
    print("\n💡 建议:")
    print("1. 日常使用 partition_overwrite 策略")
    print("2. 数据修复使用 delete_insert 策略")
    print("3. 避免使用 full_overwrite 策略")
    print("4. 在生产环境前先在开发环境测试")

if __name__ == "__main__":
    main()
