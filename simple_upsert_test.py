#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的UPSERT测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def simple_upsert_test():
    """简单的UPSERT测试"""
    print("🧪 简单的UPSERT测试...")
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.connect_db()
    
    try:
        # 1. 检查表是否存在
        print("📋 检查表结构...")
        
        try:
            check_sql = "SELECT COUNT(*) FROM dwd_asset_file_details LIMIT 1"
            processor.cursor.execute(check_sql)
            result = processor.cursor.fetchone()
            print(f"✅ 表存在，当前有数据: {result[0] if result else 0} 条")
        except Exception as e:
            print(f"❌ 表不存在或查询失败: {str(e)}")
            return
        
        # 2. 测试UPSERT功能
        print("\n🧪 测试UPSERT功能...")
        
        test_path = '/test/simple_upsert.txt'
        
        # 第一次插入
        print("📊 第一次插入数据...")
        insert1_sql = f"""
        INSERT INTO dwd_asset_file_details
        VALUES (MURMUR_HASH3_32('{test_path}'), '{test_path}', 1024, '2025-07-25 10:00:00', 'test1', 'dataset1', 'source1')
        """
        
        try:
            processor.cursor.execute(insert1_sql)
            processor.conn.commit()
            print("✅ 第一次插入成功")
        except Exception as e:
            print(f"❌ 第一次插入失败: {str(e)}")
            return
        
        # 第二次插入相同hash_key（应该更新）
        print("📊 第二次插入相同hash_key数据（测试UPSERT）...")
        insert2_sql = f"""
        INSERT INTO dwd_asset_file_details
        VALUES (MURMUR_HASH3_32('{test_path}'), '{test_path}', 2048, '2025-07-25 11:00:00', 'test2_updated', 'dataset2_updated', 'source2_updated')
        """
        
        try:
            processor.cursor.execute(insert2_sql)
            processor.conn.commit()
            print("✅ 第二次插入成功（应该是UPSERT操作）")
        except Exception as e:
            print(f"❌ 第二次插入失败: {str(e)}")
            return
        
        # 3. 验证结果
        print("\n🔍 验证UPSERT结果...")
        
        verify_sql = f"""
        SELECT hash_key, path, file_size, domain, dataset_name, source_table
        FROM dwd_asset_file_details
        WHERE path = '{test_path}'
        """
        
        try:
            processor.cursor.execute(verify_sql)
            results = processor.cursor.fetchall()
            
            print(f"📊 查询到 {len(results)} 条记录:")
            for result in results:
                print(f"   hash_key: {result[0]}")
                print(f"   path: {result[1]}")
                print(f"   file_size: {result[2]}")
                print(f"   domain: {result[3]}")
                print(f"   dataset_name: {result[4]}")
                print(f"   source_table: {result[5]}")
                print("   ---")
            
            if len(results) == 1 and results[0][2] == 2048 and results[0][3] == 'test2_updated':
                print("🎉 UPSERT功能验证成功！")
                print("   ✅ 只有一条记录（没有重复）")
                print("   ✅ 数据已更新为最新值")
            elif len(results) > 1:
                print("❌ UPSERT失败：存在重复记录")
            else:
                print("❌ UPSERT失败：数据未正确更新")
                
        except Exception as e:
            print(f"❌ 验证查询失败: {str(e)}")
        
        # 4. 清理测试数据
        print("\n🧹 清理测试数据...")
        cleanup_sql = f"DELETE FROM dwd_asset_file_details WHERE path = '{test_path}'"
        try:
            processor.cursor.execute(cleanup_sql)
            processor.conn.commit()
            print("✅ 测试数据已清理")
        except Exception as e:
            print(f"⚠️ 清理失败: {str(e)}")
        
        print("\n📋 测试总结:")
        print("   如果UPSERT功能正常，说明您的hash_key PRIMARY KEY表配置正确")
        print("   现在可以直接使用 native_merge 策略进行ETL处理")
        print("   命令: python etl_processor.py --env dev --incremental-strategy native_merge")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
    
    finally:
        processor.close_db()

if __name__ == "__main__":
    simple_upsert_test()
