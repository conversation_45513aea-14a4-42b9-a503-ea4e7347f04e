#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试原生MERGE功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def test_native_merge():
    """测试原生MERGE功能"""
    print("🧪 测试原生MERGE功能...")
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.target_date = '2025-07-25'
    
    # 生成原生MERGE SQL
    print("📋 生成原生MERGE SQL:")
    sql = processor.build_dwd_incremental_sql_native_merge()
    print("=" * 80)
    print(sql)
    print("=" * 80)
    
    # 连接数据库测试
    processor.connect_db()
    
    try:
        # 检查当前表结构
        desc_sql = "DESC dwd_asset_file_details"
        processor.cursor.execute(desc_sql)
        columns = processor.cursor.fetchall()
        
        print("\n📊 当前表结构:")
        for col in columns:
            print(f"   {col[0]} {col[1]} {col[2] if len(col) > 2 else ''}")
        
        # 检查表类型
        show_create_sql = "SHOW CREATE TABLE dwd_asset_file_details"
        processor.cursor.execute(show_create_sql)
        create_info = processor.cursor.fetchone()
        
        print(f"\n📋 表创建信息:")
        create_statement = create_info[1] if create_info else "未找到"
        
        if "PRIMARY KEY" in create_statement:
            print("✅ 当前表是PRIMARY KEY表，支持原生MERGE")
        elif "DUPLICATE KEY" in create_statement:
            print("⚠️ 当前表是DUPLICATE KEY表，需要迁移到PRIMARY KEY表")
        else:
            print("❓ 无法确定表类型")
        
        # 如果是PRIMARY KEY表，测试MERGE功能
        if "PRIMARY KEY" in create_statement:
            print("\n🧪 测试MERGE操作...")
            
            # 简单的MERGE测试
            test_merge_sql = """
            INSERT INTO dwd_asset_file_details
            VALUES ('/test/merge_test.txt', '2025-07-25 10:00:00', 1024, 'test', 'test_dataset', 'test_source')
            ON DUPLICATE KEY UPDATE
                file_size = VALUES(file_size),
                domain = VALUES(domain),
                dataset_name = VALUES(dataset_name),
                source_table = VALUES(source_table)
            """
            
            try:
                processor.cursor.execute(test_merge_sql)
                processor.conn.commit()
                print("✅ MERGE操作测试成功")
                
                # 清理测试数据
                cleanup_sql = "DELETE FROM dwd_asset_file_details WHERE path = '/test/merge_test.txt'"
                processor.cursor.execute(cleanup_sql)
                processor.conn.commit()
                print("✅ 测试数据已清理")
                
            except Exception as e:
                print(f"❌ MERGE操作测试失败: {str(e)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    finally:
        processor.close_db()

if __name__ == "__main__":
    test_native_merge()
