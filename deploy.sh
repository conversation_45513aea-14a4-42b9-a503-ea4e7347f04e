#!/bin/bash

# Superset数据仓库ETL任务部署脚本
# 使用方法: ./deploy.sh [dev|prod]

set -e

# 默认环境为dev
ENV=${1:-dev}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境参数
if [[ "$ENV" != "dev" && "$ENV" != "prod" ]]; then
    log_error "无效的环境参数: $ENV"
    log_info "使用方法: ./deploy.sh [dev|prod]"
    exit 1
fi

log_info "开始部署Superset数据仓库ETL任务到 $ENV 环境"

# 设置环境相关变量
if [[ "$ENV" == "dev" ]]; then
    NAMESPACE="data-assets-dev-ns"
    CONFIGMAP_NAME="superset-dw-etl-files-dev"
    CRONJOB_FILE="superset-dw-etl-cronjob-dev.yaml"
    JOB_NAME="superset-dw-etl-job-dev"
else
    NAMESPACE="data-assets-prod-ns"
    CONFIGMAP_NAME="superset-dw-etl-files"
    CRONJOB_FILE="superset-dw-etl-cronjob.yaml"
    JOB_NAME="superset-dw-etl-job"
fi

log_info "目标命名空间: $NAMESPACE"
log_info "ConfigMap名称: $CONFIGMAP_NAME"

# 检查必要文件是否存在
if [[ ! -f "etl_processor.py" ]]; then
    log_error "etl_processor.py 文件不存在"
    exit 1
fi

if [[ ! -f "requirements.txt" ]]; then
    log_error "requirements.txt 文件不存在"
    exit 1
fi

if [[ ! -f "$CRONJOB_FILE" ]]; then
    log_error "$CRONJOB_FILE 文件不存在"
    exit 1
fi

# 检查kubectl是否可用
if ! command -v kubectl &> /dev/null; then
    log_error "kubectl 命令不可用，请确保已安装并配置kubectl"
    exit 1
fi

# 检查命名空间是否存在
if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
    log_warning "命名空间 $NAMESPACE 不存在，正在创建..."
    kubectl create namespace "$NAMESPACE"
    log_success "命名空间 $NAMESPACE 创建成功"
fi

# 删除旧的ConfigMap（如果存在）
if kubectl get configmap "$CONFIGMAP_NAME" -n "$NAMESPACE" &> /dev/null; then
    log_info "删除旧的ConfigMap: $CONFIGMAP_NAME"
    kubectl delete configmap "$CONFIGMAP_NAME" -n "$NAMESPACE"
fi

# 创建新的ConfigMap
log_info "创建ConfigMap: $CONFIGMAP_NAME"
kubectl create configmap "$CONFIGMAP_NAME" \
    --from-file=etl_processor.py \
    --from-file=requirements.txt \
    -n "$NAMESPACE"

log_success "ConfigMap创建成功"

# 删除旧的CronJob（如果存在）
if kubectl get cronjob "$JOB_NAME" -n "$NAMESPACE" &> /dev/null; then
    log_info "删除旧的CronJob: $JOB_NAME"
    kubectl delete cronjob "$JOB_NAME" -n "$NAMESPACE"
fi

# 部署CronJob
log_info "部署CronJob: $JOB_NAME"
kubectl apply -f "$CRONJOB_FILE"

log_success "CronJob部署成功"

# 显示部署状态
log_info "检查部署状态..."
kubectl get cronjob "$JOB_NAME" -n "$NAMESPACE"

log_success "Superset数据仓库ETL任务部署完成！"

# 显示有用的命令
echo ""
log_info "有用的命令:"
echo "  查看CronJob状态: kubectl get cronjob $JOB_NAME -n $NAMESPACE"
echo "  查看Job历史: kubectl get jobs -n $NAMESPACE | grep $JOB_NAME"
echo "  查看Pod日志: kubectl logs -f <pod-name> -n $NAMESPACE"
echo "  手动触发任务: kubectl create job --from=cronjob/$JOB_NAME manual-run-\$(date +%s) -n $NAMESPACE"
echo "  删除CronJob: kubectl delete cronjob $JOB_NAME -n $NAMESPACE"
