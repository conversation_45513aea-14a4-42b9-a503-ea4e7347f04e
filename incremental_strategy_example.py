#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
StarRocks增量处理策略使用示例

本脚本展示了如何使用不同的增量处理策略来优化DWD层数据处理性能。

性能对比:
- partition_overwrite: 2-5分钟 (推荐)
- delete_insert: 5-10分钟
- merge: 3-8分钟  
- full_overwrite: 2-4小时 (不推荐)
"""

import sys
import os
import datetime
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def main():
    parser = argparse.ArgumentParser(description='StarRocks增量处理策略示例')
    parser.add_argument('--env', choices=['dev', 'prod'], default='dev', 
                       help='运行环境 (默认: dev)')
    parser.add_argument('--strategy', 
                       choices=['partition_overwrite', 'delete_insert', 'merge', 'full_overwrite'],
                       default='partition_overwrite',
                       help='增量处理策略 (默认: partition_overwrite)')
    parser.add_argument('--target-date', type=str, 
                       help='目标处理日期 (格式: YYYY-MM-DD，默认: 当日)')
    parser.add_argument('--dry-run', action='store_true',
                       help='仅显示SQL，不执行')
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("🚀 StarRocks增量处理策略优化示例")
    print("=" * 80)
    
    # 创建ETL处理器
    processor = SupersetDWETLProcessor(env=args.env)
    
    # 设置增量策略
    processor.set_incremental_strategy(args.strategy)
    
    # 设置目标日期
    if args.target_date:
        processor.target_date = args.target_date
        print(f"📅 目标处理日期: {args.target_date}")
    else:
        print(f"📅 目标处理日期: {datetime.datetime.now().strftime('%Y-%m-%d')} (当日)")
    
    print(f"🏗️ 运行环境: {args.env}")
    print(f"📋 增量策略: {args.strategy}")
    
    if args.dry_run:
        print("\n" + "=" * 60)
        print("🔍 DRY RUN 模式 - 仅显示SQL")
        print("=" * 60)
        
        # 显示对应的SQL
        if args.strategy == 'partition_overwrite':
            sql = processor.build_dwd_incremental_sql()
        elif args.strategy == 'delete_insert':
            sql = processor.build_dwd_incremental_sql_delete_insert()
        elif args.strategy == 'merge':
            sql = processor.build_dwd_incremental_sql_merge()
        elif args.strategy == 'full_overwrite':
            sql = processor.build_dwd_incremental_sql_full_overwrite()
        
        print("📋 生成的SQL:")
        print("-" * 60)
        print(sql)
        print("-" * 60)
        
    else:
        print("\n" + "=" * 60)
        print("⚡ 执行增量处理")
        print("=" * 60)
        
        try:
            # 连接数据库
            processor.connect_db()
            
            # 执行DWD层处理
            success = processor.process_dwd_layer()
            
            if success:
                print("🎉 增量处理完成！")
                
                # 显示性能统计
                print("\n📊 性能统计:")
                print(f"   策略: {args.strategy}")
                print(f"   状态: {'成功' if success else '失败'}")
                
            else:
                print("💥 增量处理失败！")
                
        except Exception as e:
            print(f"❌ 执行过程中发生错误: {str(e)}")
            
        finally:
            processor.close_db()
    
    print("\n" + "=" * 80)
    print("📚 策略选择建议:")
    print("=" * 80)
    print("🌟 partition_overwrite: 性能最优，推荐用于日常增量处理")
    print("✅ delete_insert: 逻辑清晰，适合需要明确删除再插入的场景")
    print("✅ merge: 适合频繁更新的场景，支持UPSERT语义")
    print("❌ full_overwrite: 仅作备选，不推荐用于大数据量场景")
    print("\n💡 对于18亿+数据量，建议使用 partition_overwrite 策略")

if __name__ == "__main__":
    main()
