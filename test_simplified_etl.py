#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
精简ETL处理器测试脚本
用于验证数据库连接和基本功能
"""

import sys
import logging
from simplified_etl_processor import SimplifiedETLProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_simplified_etl')

def test_database_connection(env='dev'):
    """测试数据库连接"""
    logger.info(f"🔍 测试{env}环境数据库连接...")
    
    try:
        processor = SimplifiedETLProcessor(env=env)
        processor.connect_db()
        
        # 测试基本查询
        test_sql = "SELECT 1 as test_value"
        processor.cursor.execute(test_sql)
        result = processor.cursor.fetchone()
        
        if result and result['test_value'] == 1:
            logger.info("✅ 数据库连接测试成功")
            return True
        else:
            logger.error("❌ 数据库查询结果异常")
            return False
            
    except Exception as e:
        logger.error(f"❌ 数据库连接测试失败: {str(e)}")
        return False
    finally:
        try:
            processor.close_db()
        except:
            pass

def test_table_existence(env='dev'):
    """测试目标表是否存在"""
    logger.info(f"🔍 测试{env}环境目标表是否存在...")
    
    tables = [
        'dwd_asset_file_details',
        'dws_asset_storage_trend_daily', 
        'dws_asset_storage_trend_weekly',
        'dws_asset_domain_distribution'
    ]
    
    try:
        processor = SimplifiedETLProcessor(env=env)
        processor.connect_db()
        
        results = {}
        for table in tables:
            try:
                check_sql = f"SELECT COUNT(*) as row_count FROM {table} LIMIT 1"
                processor.cursor.execute(check_sql)
                result = processor.cursor.fetchone()
                results[table] = True
                logger.info(f"✅ 表 {table} 存在，当前行数: {result['row_count']:,}")
            except Exception as e:
                results[table] = False
                logger.error(f"❌ 表 {table} 不存在或无法访问: {str(e)}")
        
        success_count = sum(1 for exists in results.values() if exists)
        total_count = len(results)
        
        logger.info(f"📊 表存在性检查结果: {success_count}/{total_count} 个表可访问")
        
        return success_count == total_count
        
    except Exception as e:
        logger.error(f"❌ 表存在性检查失败: {str(e)}")
        return False
    finally:
        try:
            processor.close_db()
        except:
            pass

def test_source_tables(env='dev'):
    """测试源表数据"""
    logger.info(f"🔍 测试{env}环境源表数据...")
    
    source_tables = [
        'tp_crawler_record',
        'tp_compare_crawler_282',
        'mutimodel_processed',
        'crawler_filescan_extra_hwy',
        'fuxi_scan',
        'crawler_filescan_extra'
    ]
    
    try:
        processor = SimplifiedETLProcessor(env=env)
        processor.connect_db()
        
        results = {}
        total_rows = 0
        
        for table in source_tables:
            try:
                check_sql = f"SELECT COUNT(*) as row_count FROM {table}"
                processor.cursor.execute(check_sql)
                result = processor.cursor.fetchone()
                row_count = result['row_count']
                results[table] = row_count
                total_rows += row_count
                
                if row_count > 0:
                    logger.info(f"✅ 源表 {table}: {row_count:,} 行")
                else:
                    logger.warning(f"⚠️ 源表 {table}: 无数据")
                    
            except Exception as e:
                results[table] = 0
                logger.error(f"❌ 源表 {table} 无法访问: {str(e)}")
        
        logger.info(f"📊 源表数据统计: 总计 {total_rows:,} 行数据")
        
        return total_rows > 0
        
    except Exception as e:
        logger.error(f"❌ 源表数据检查失败: {str(e)}")
        return False
    finally:
        try:
            processor.close_db()
        except:
            pass

def test_task_name_generation():
    """测试任务名称生成"""
    logger.info("🔍 测试任务名称生成...")

    try:
        import time
        processor = SimplifiedETLProcessor()

        # 生成几个任务名称，添加微小延迟确保唯一性
        task_names = []
        for i in range(3):
            task_name = processor.generate_task_name("test_table")
            task_names.append(task_name)
            logger.info(f"生成任务名称 {i+1}: {task_name}")
            time.sleep(0.001)  # 1毫秒延迟确保时间戳不同

        # 检查任务名称是否唯一
        if len(set(task_names)) == len(task_names):
            logger.info("✅ 任务名称生成测试成功 - 所有名称唯一")
            return True
        else:
            logger.error("❌ 任务名称生成测试失败 - 存在重复名称")
            logger.error(f"   生成的名称: {task_names}")
            return False

    except Exception as e:
        logger.error(f"❌ 任务名称生成测试失败: {str(e)}")
        return False

def run_all_tests(env='dev'):
    """运行所有测试"""
    logger.info("=" * 60)
    logger.info(f"🧪 开始运行精简ETL处理器测试 (环境: {env})")
    logger.info("=" * 60)
    
    tests = [
        ("数据库连接测试", lambda: test_database_connection(env)),
        ("目标表存在性测试", lambda: test_table_existence(env)),
        ("源表数据测试", lambda: test_source_tables(env)),
        ("任务名称生成测试", test_task_name_generation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🔬 执行测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name}: 通过")
            else:
                logger.error(f"❌ {test_name}: 失败")
                
        except Exception as e:
            results[test_name] = False
            logger.error(f"💥 {test_name}: 异常 - {str(e)}")
    
    # 统计结果
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    success_rate = (success_count / total_count) * 100
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 测试结果统计")
    logger.info("=" * 60)
    logger.info(f"总测试数: {total_count}")
    logger.info(f"通过测试: {success_count}")
    logger.info(f"失败测试: {total_count - success_count}")
    logger.info(f"成功率: {success_rate:.1f}%")
    
    logger.info("\n📋 详细结果:")
    for test_name, success in results.items():
        icon = "✅" if success else "❌"
        status = "通过" if success else "失败"
        logger.info(f"   {icon} {test_name}: {status}")
    
    logger.info("\n🎯 总体评估:")
    if success_rate == 100:
        logger.info("   🎉 所有测试通过！可以安全执行ETL任务")
        return 0
    elif success_rate >= 75:
        logger.warning("   ⚠️ 大部分测试通过，请检查失败的测试项")
        return 1
    else:
        logger.error("   ❌ 多个测试失败，请修复问题后再执行ETL任务")
        return 2

if __name__ == '__main__':
    # 从命令行参数获取环境
    env = 'dev'
    if len(sys.argv) > 1:
        env = sys.argv[1]
        if env not in ['dev', 'prod']:
            logger.error("❌ 无效的环境参数，请使用 'dev' 或 'prod'")
            sys.exit(1)
    
    exit_code = run_all_tests(env)
    sys.exit(exit_code)
