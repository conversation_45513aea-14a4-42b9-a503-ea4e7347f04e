#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
精简ETL处理器配置文件
集中管理数据库配置、超时设置、重试参数等
"""

import os
from typing import Dict, Any

# 数据库配置
DB_CONFIG = {
    'dev': {
        'host': '************',
        'port': 30000,
        'user': 'root',
        'password': '',
        'db': 'CRAWLER',
        'charset': 'utf8mb4',
        'connect_timeout': 30,
        'read_timeout': 300,
        'write_timeout': 300
    },
    'prod': {
        'host': 'kube-starrocks-fe-search.data-infra-prod-ns',
        'port': 9030,
        'user': 'root',
        'password': '',
        'db': 'CRAWLER',
        'charset': 'utf8mb4',
        'connect_timeout': 30,
        'read_timeout': 300,
        'write_timeout': 300
    }
}

# 任务超时配置 (秒)
TASK_TIMEOUT_CONFIG = {
    'dwd_asset_file_details': {
        'query_timeout': 14400,  # 4小时
        'max_wait_seconds': 18000  # 5小时
    },
    'dws_asset_storage_trend_daily': {
        'query_timeout': 1800,  # 30分钟
        'max_wait_seconds': 2400  # 40分钟
    },
    'dws_asset_storage_trend_weekly': {
        'query_timeout': 1800,  # 30分钟
        'max_wait_seconds': 2400  # 40分钟
    },
    'dws_asset_domain_distribution': {
        'query_timeout': 1200,  # 20分钟
        'max_wait_seconds': 1800  # 30分钟
    }
}

# 重试配置
RETRY_CONFIG = {
    'max_retries': 3,
    'retry_delay': 10,  # 秒
    'backoff_factor': 1.5,  # 指数退避因子
    'max_retry_delay': 300  # 最大重试延迟(秒)
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_max_bytes': 50 * 1024 * 1024,  # 50MB
    'backup_count': 5,
    'encoding': 'utf-8'
}

# 日志目录配置
def get_log_directory():
    """获取日志目录路径"""
    if os.path.exists('/app'):
        return '/app/logs/superset_dw_etl'
    else:
        return os.path.join(os.getcwd(), 'logs')

# 表配置
TABLE_CONFIG = {
    'dwd_asset_file_details': {
        'type': 'dwd',
        'execution_mode': 'async',
        'description': 'DWD层文件详情明细表',
        'dependencies': [],
        'primary_key': 'hash_key',
        'expected_min_rows': 0  # DWD表可能为空
    },
    'dws_asset_storage_trend_daily': {
        'type': 'dws',
        'execution_mode': 'sync',
        'description': 'DWS层日度存储趋势表',
        'dependencies': ['dwd_asset_file_details'],
        'primary_key': 'stat_date',
        'expected_min_rows': 1
    },
    'dws_asset_storage_trend_weekly': {
        'type': 'dws',
        'execution_mode': 'sync',
        'description': 'DWS层周度存储趋势表',
        'dependencies': ['dwd_asset_file_details'],
        'primary_key': 'week_start_date',
        'expected_min_rows': 1
    },
    'dws_asset_domain_distribution': {
        'type': 'dws',
        'execution_mode': 'sync',
        'description': 'DWS层领域分布表',
        'dependencies': ['dwd_asset_file_details'],
        'primary_key': 'domain',
        'expected_min_rows': 1
    }
}

# 源表配置
SOURCE_TABLE_CONFIG = {
    'tp_crawler_record': {
        'priority': 1,
        'description': '核心采集表',
        'path_prefix': None,
        'required': True
    },
    'tp_compare_crawler_282': {
        'priority': 2,
        'description': '历史数据表',
        'path_prefix': None,
        'required': False
    },
    'mutimodel_processed': {
        'priority': 2,
        'description': '多模态处理表',
        'path_prefix': '/cpfs01/projects-HDD/cfff-85cad58c20e7_HDD/public',
        'required': False
    },
    'crawler_filescan_extra_hwy': {
        'priority': 2,
        'description': '文件扫描扩展表(hwy)',
        'path_prefix': '/cfff-4a8d9af84f66_HDD/public',
        'required': False
    },
    'fuxi_scan': {
        'priority': 2,
        'description': '伏羲扫描表',
        'path_prefix': [
            '/cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD/public',
            '/cpfs01/projects-HDD/cfff-01ff502a0784_HDD/public'
        ],
        'required': False
    },
    'crawler_filescan_extra': {
        'priority': 2,
        'description': '文件扫描扩展表',
        'path_prefix': '/cpfs01/projects-HDD/cfff-4a8d9af84f66_HDD/public',
        'required': False
    }
}

# 监控配置
MONITORING_CONFIG = {
    'task_check_interval': 15,  # 异步任务状态检查间隔(秒)
    'heartbeat_interval': 60,   # 心跳日志间隔(秒)
    'progress_log_threshold': 5,  # 进度变化记录阈值(%)
    'enable_performance_metrics': True,
    'enable_data_quality_checks': True
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    'batch_size': 10000,
    'parallel_workers': 3,
    'memory_limit_mb': 2048,
    'temp_table_prefix': 'tmp_simplified_etl_',
    'enable_query_cache': True,
    'enable_result_cache': False
}

# 数据质量配置
DATA_QUALITY_CONFIG = {
    'enable_null_checks': True,
    'enable_duplicate_checks': True,
    'enable_range_checks': True,
    'max_null_percentage': 10.0,  # 最大空值百分比
    'max_duplicate_percentage': 5.0,  # 最大重复百分比
    'date_range_days': 3650  # 日期范围检查天数(10年)
}

def get_config(config_type: str, env: str = 'dev') -> Dict[str, Any]:
    """
    获取指定类型的配置
    
    Args:
        config_type: 配置类型 ('db', 'timeout', 'retry', 'log', 'table', 'source', 'monitoring', 'performance', 'quality')
        env: 环境 ('dev', 'prod')
    
    Returns:
        配置字典
    """
    config_map = {
        'db': DB_CONFIG.get(env, DB_CONFIG['dev']),
        'timeout': TASK_TIMEOUT_CONFIG,
        'retry': RETRY_CONFIG,
        'log': LOG_CONFIG,
        'table': TABLE_CONFIG,
        'source': SOURCE_TABLE_CONFIG,
        'monitoring': MONITORING_CONFIG,
        'performance': PERFORMANCE_CONFIG,
        'quality': DATA_QUALITY_CONFIG
    }
    
    return config_map.get(config_type, {})

def get_table_timeout(table_name: str) -> Dict[str, int]:
    """获取指定表的超时配置"""
    return TASK_TIMEOUT_CONFIG.get(table_name, {
        'query_timeout': 3600,
        'max_wait_seconds': 7200
    })

def get_table_info(table_name: str) -> Dict[str, Any]:
    """获取指定表的配置信息"""
    return TABLE_CONFIG.get(table_name, {})

def is_table_required(table_name: str) -> bool:
    """检查表是否为必需表"""
    table_info = get_table_info(table_name)
    return table_info.get('required', True)

def get_source_table_info(table_name: str) -> Dict[str, Any]:
    """获取源表配置信息"""
    return SOURCE_TABLE_CONFIG.get(table_name, {})

def validate_config(env: str = 'dev') -> bool:
    """
    验证配置的有效性
    
    Args:
        env: 环境
        
    Returns:
        配置是否有效
    """
    try:
        # 检查数据库配置
        db_config = get_config('db', env)
        required_db_keys = ['host', 'port', 'user', 'db']
        for key in required_db_keys:
            if key not in db_config:
                raise ValueError(f"数据库配置缺少必需字段: {key}")
        
        # 检查超时配置
        timeout_config = get_config('timeout')
        for table_name, config in timeout_config.items():
            if 'query_timeout' not in config or 'max_wait_seconds' not in config:
                raise ValueError(f"表 {table_name} 超时配置不完整")
        
        # 检查表配置
        table_config = get_config('table')
        for table_name, config in table_config.items():
            required_keys = ['type', 'execution_mode', 'description']
            for key in required_keys:
                if key not in config:
                    raise ValueError(f"表 {table_name} 配置缺少必需字段: {key}")
        
        return True
        
    except Exception as e:
        print(f"配置验证失败: {str(e)}")
        return False

if __name__ == '__main__':
    # 配置验证测试
    print("🔍 验证配置文件...")
    
    for env in ['dev', 'prod']:
        print(f"\n📋 验证 {env} 环境配置:")
        if validate_config(env):
            print(f"✅ {env} 环境配置验证通过")
        else:
            print(f"❌ {env} 环境配置验证失败")
    
    print("\n📊 配置概览:")
    print(f"- 支持环境: {list(DB_CONFIG.keys())}")
    print(f"- 目标表数量: {len(TABLE_CONFIG)}")
    print(f"- 源表数量: {len(SOURCE_TABLE_CONFIG)}")
    print(f"- 日志目录: {get_log_directory()}")
    
    print("\n🎯 表处理顺序:")
    dwd_tables = [name for name, config in TABLE_CONFIG.items() if config['type'] == 'dwd']
    dws_tables = [name for name, config in TABLE_CONFIG.items() if config['type'] == 'dws']
    
    print("1. DWD层 (异步):")
    for table in dwd_tables:
        print(f"   - {table}")
    
    print("2. DWS层 (同步):")
    for table in dws_tables:
        print(f"   - {table}")
