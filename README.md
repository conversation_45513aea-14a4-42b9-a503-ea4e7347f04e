# Superset数据仓库ETL任务

这是一个完整的数据仓库ETL处理脚本，用于处理Superset数据分析平台的数据层次结构。

## 功能概述

### 数据分层架构
- **DWD层 (Data Warehouse Detail)**: `dwd_asset_file_details` - 明细数据层，整合多个数据源并去重
- **DWS层 (Data Warehouse Summary)**: 汇总数据层，包含三个表：
  - `dws_asset_storage_trend_daily` - 日度存储趋势
  - `dws_asset_storage_trend_weekly` - 周度存储趋势  
  - `dws_asset_domain_distribution` - 领域分布统计

### 核心特性
- ✅ **异步任务管理**: 使用StarRocks SUBMIT TASK实现真正的异步执行控制
- ✅ **任务状态监控**: 通过information_schema.task_runs实时监控任务状态
- ✅ **分层处理**: DWD层完成后才执行DWS层，确保数据依赖关系
- ✅ **并行执行**: DWS层三个表可以并行处理，提高执行效率
- ✅ **数据验证**: 每个表处理完成后自动验证数据完整性
- ✅ **重试机制**: 任务提交失败时自动重试，提高稳定性
- ✅ **详细日志**: 完整的执行日志和进度跟踪，包含任务状态变化
- ✅ **调试模式**: 支持调试模式，便于开发和测试

## 文件结构

```
superset_dw_job/
├── etl_processor.py                    # 主ETL处理脚本
├── run_etl.sh                          # 便捷运行脚本
├── requirements.txt                    # Python依赖包
├── superset-dw-etl-cronjob.yaml       # 生产环境K8s CronJob配置
├── superset-dw-etl-cronjob-dev.yaml   # 开发环境K8s CronJob配置
├── deploy.sh                          # 部署脚本
├── build_and_push.sh                  # Docker镜像构建脚本
└── README.md                          # 说明文档
```

## 快速开始

### 1. 本地测试

```bash
# 安装依赖
pip install -r requirements.txt

# 使用便捷脚本（推荐）
./run_etl.sh dev          # 开发环境，默认8次重试，60秒延迟
./run_etl.sh prod 10 90   # 生产环境，10次重试，90秒延迟

# 直接使用Python脚本
python etl_processor.py --env dev --log-level INFO
python etl_processor.py --env prod --log-level INFO --max-retries 10 --retry-delay 90
```

### 2. K8s部署

```bash
# 部署到开发环境
./deploy.sh dev

# 部署到生产环境
./deploy.sh prod
```

## 执行逻辑

### 异步任务管理机制
1. **任务提交**: 使用`SUBMIT TASK AS 'task_name' SQL`提交异步任务
2. **唯一命名**: 任务名格式为`表名_时间戳`，确保唯一性
3. **状态监控**: 通过`information_schema.task_runs`查询任务状态
4. **依赖控制**: 基于任务状态控制执行流程，确保数据依赖关系

### 第一阶段：DWD层处理
1. 生成唯一任务名：`dwd_asset_file_details_20250118_143025_123`
2. 提交异步任务到StarRocks
3. 轮询任务状态直到SUCCESS或FAILED
4. 验证表数据完整性
5. 只有DWD层完全成功才进入下一阶段

**数据处理逻辑：**
- 整合6个数据源表的数据
- 标准化字段格式（如domain字段清洗）
- 处理路径前缀，统一路径格式
- 按path去重，优先保留高优先级和最新数据
- 插入到`dwd_asset_file_details`表

### 第二阶段：DWS层并行处理
DWD层成功后，并行提交三个异步任务：

1. **日度趋势分析** (`dws_asset_storage_trend_daily`)
   - 任务名：`dws_asset_storage_trend_daily_时间戳`
   - 按天聚合存储量，计算累计存储量和日环比增长率

2. **周度趋势分析** (`dws_asset_storage_trend_weekly`)
   - 任务名：`dws_asset_storage_trend_weekly_时间戳`
   - 按周聚合存储量，计算累计存储量和周环比增长率

3. **领域分布统计** (`dws_asset_domain_distribution`)
   - 任务名：`dws_asset_domain_distribution_时间戳`
   - 按领域统计数据集数量和存储大小

**并行执行流程：**
- 使用ThreadPoolExecutor同时提交3个任务
- 每个任务独立监控状态直到完成
- 验证每个表的数据完整性
- 所有DWS任务完成后才算整体成功

## 数据源说明

脚本整合以下6个数据源表：

1. `tp_crawler_record` - 核心采集表（最高优先级）
2. `tp_compare_crawler_282` - 比较采集表
3. `mutimodel_processed` - 多模型处理表
4. `crawler_filescan_extra_hwy` - 额外文件扫描表
5. `fuxi_scan` - 伏羲扫描表
6. `crawler_filescan_extra` - 额外文件扫描表

## 调度配置

### 生产环境
- **执行时间**: 每天上午10:00
- **并发策略**: 禁止并发执行
- **资源配置**: 2GB内存，2核CPU

### 开发环境  
- **执行时间**: 每30分钟（便于测试）
- **并发策略**: 禁止并发执行
- **资源配置**: 1GB内存，1核CPU

## 监控和运维

### 查看任务状态
```bash
# 查看CronJob状态
kubectl get cronjob superset-dw-etl-job -n data-assets-prod-ns

# 查看Job执行历史
kubectl get jobs -n data-assets-prod-ns | grep superset-dw-etl

# 查看Pod日志
kubectl logs -f <pod-name> -n data-assets-prod-ns
```

### 手动触发任务
```bash
# 手动创建一次性Job
kubectl create job --from=cronjob/superset-dw-etl-job manual-run-$(date +%s) -n data-assets-prod-ns
```

### 日志文件
- 容器内日志路径: `/app/logs/superset_dw_etl/`
- 日志文件格式: `etl_processor-YYYYMMDD.log`

## 错误处理

### 常见问题
1. **数据库连接失败**: 检查网络连接和数据库配置
2. **SQL执行超时**: 检查数据量和数据库性能
3. **内存不足**: 调整K8s资源限制

### 重试机制
- SQL执行失败时自动重试3次
- 重试间隔递增：2秒、4秒、6秒

### 事务回滚
- 每个SQL语句都在事务中执行
- 失败时自动回滚，保证数据一致性

## 性能优化

### 并行处理
- DWS层三个表并行处理，减少总执行时间
- 使用ThreadPoolExecutor管理线程池

### 资源配置
- 根据数据量调整内存和CPU限制
- 生产环境建议2GB内存以上

## 开发说明

### 添加新的DWS表
1. 在`SupersetDWETLProcessor`类中添加新的处理方法
2. 在`process_dws_layer_parallel`方法中添加新任务
3. 更新`task_stats`和`print_summary`方法

### 修改数据源
1. 在`process_dwd_layer`方法中修改SQL语句
2. 添加或删除UNION ALL子句
3. 调整优先级和字段映射

## 注意事项

⚠️ **重要提醒**:
- 生产环境执行前请先在开发环境充分测试
- 确保目标表结构已在StarRocks中创建
- 监控执行时间，避免影响业务高峰期
- 定期检查日志文件，及时发现和解决问题


日常执行: python3 etl_processor.py --env prod (增量模式)
初始化: python3 etl_processor.py --env prod --init-mode (全量模式)
数据修复: python3 etl_processor.py --env prod --target-date 2024-12-21 (指定日期)
定时任务: 配置每日自动执行增量模式




docker build --platform linux/amd64 -t harbor-paas.internal.sais.com.cn/assets-platform/etl_processor_py:1.0.0 --push .