#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试哈希主键方案
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from etl_processor import SupersetDWETLProcessor

def test_hash_primary_key():
    """测试哈希主键方案"""
    print("🧪 测试哈希主键方案...")
    
    processor = SupersetDWETLProcessor(env='dev')
    processor.connect_db()
    
    try:
        # 1. 创建哈希主键测试表
        print("📋 创建哈希主键测试表...")
        
        drop_sql = "DROP TABLE IF EXISTS dwd_asset_file_details"
        processor.cursor.execute(drop_sql)
        processor.conn.commit()
        
        create_sql = """
        CREATE TABLE `dwd_asset_file_details` (
          `path_hash` int NOT NULL COMMENT "路径哈希值(主键)",
          `path` varchar(65533) NOT NULL COMMENT "文件唯一路径",
          `file_size` bigint(20) NULL COMMENT "文件大小 (Bytes)",
          `cre_dt` datetime NULL COMMENT "文件创建时间",
          `domain` varchar(255) NULL COMMENT "所属领域",
          `dataset_name` varchar(255) NULL COMMENT "所属数据集名称",
          `source_table` varchar(50) NULL COMMENT "数据来源表"
        ) ENGINE=OLAP 
        PRIMARY KEY(`path_hash`)
        DISTRIBUTED BY HASH(`path_hash`) BUCKETS 128 
        PROPERTIES (
        "replication_num" = "3",
        "in_memory" = "false",
        "enable_persistent_index" = "false",
        "replicated_storage" = "true",
        "storage_medium" = "HDD",
        "compression" = "LZ4"
        );
        """
        
        processor.cursor.execute(create_sql)
        processor.conn.commit()
        print("✅ 哈希主键表创建成功")
        
        # 2. 插入测试数据
        print("📊 插入测试数据...")
        
        insert_sql = """
        INSERT INTO dwd_asset_file_details VALUES
        (MURMUR_HASH3_32('/test/file1.txt'), '/test/file1.txt', 1024, '2025-07-25 10:00:00', 'test', 'test_dataset', 'test_source'),
        (MURMUR_HASH3_32('/test/file2.txt'), '/test/file2.txt', 2048, '2025-07-25 11:00:00', 'test', 'test_dataset', 'test_source')
        """
        
        processor.cursor.execute(insert_sql)
        processor.conn.commit()
        print("✅ 测试数据插入成功")
        
        # 3. 测试UPSERT功能（PRIMARY KEY表的自动UPSERT）
        print("🧪 测试UPSERT功能...")

        # PRIMARY KEY表会自动处理重复主键，新数据会覆盖旧数据
        upsert_sql = """
        INSERT INTO dwd_asset_file_details
        VALUES (MURMUR_HASH3_32('/test/file1.txt'), '/test/file1.txt', 1536, '2025-07-25 12:00:00', 'test_updated', 'test_dataset_updated', 'test_source_updated')
        """

        processor.cursor.execute(upsert_sql)
        processor.conn.commit()
        print("✅ UPSERT操作成功（PRIMARY KEY表自动处理重复键）")
        
        # 4. 验证结果
        print("🔍 验证结果...")
        
        check_sql = """
        SELECT path, file_size, cre_dt, domain, dataset_name, source_table
        FROM dwd_asset_file_details
        ORDER BY path
        """
        
        processor.cursor.execute(check_sql)
        results = processor.cursor.fetchall()
        
        print("📊 验证结果:")
        for result in results:
            print(f"   路径: {result[0]}")
            print(f"   大小: {result[1]}")
            print(f"   时间: {result[2]}")
            print(f"   域名: {result[3]}")
            print(f"   数据集: {result[4]}")
            print(f"   来源: {result[5]}")
            print("   ---")
        
        # 5. 测试原生MERGE SQL生成
        print("📋 测试原生MERGE SQL生成...")
        processor.target_date = '2025-07-25'
        sql = processor.build_dwd_incremental_sql_native_merge()
        print("✅ 原生MERGE SQL生成成功")
        print(f"📏 SQL长度: {len(sql)} 字符")
        
        # 6. 检查哈希冲突
        print("🔍 检查哈希冲突...")
        
        conflict_sql = """
        SELECT path_hash, COUNT(DISTINCT path) as path_count
        FROM dwd_asset_file_details
        GROUP BY path_hash
        HAVING COUNT(DISTINCT path) > 1
        """
        
        processor.cursor.execute(conflict_sql)
        conflicts = processor.cursor.fetchall()
        
        if conflicts:
            print(f"⚠️ 发现 {len(conflicts)} 个哈希冲突")
            for conflict in conflicts:
                print(f"   哈希值: {conflict[0]}, 路径数: {conflict[1]}")
        else:
            print("✅ 无哈希冲突")
        
        print("\n🎉 哈希主键方案测试完成！")
        print("📋 优势:")
        print("   ✅ 支持原生MERGE操作")
        print("   ✅ 主键长度固定(32位整数)")
        print("   ✅ 无分区限制")
        print("   ✅ 查询性能优秀")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    finally:
        processor.close_db()

if __name__ == "__main__":
    test_hash_primary_key()
